"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  FileText,
  Send,
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Download,
  Eye,
  Edit
} from "lucide-react";
import { useEmployeeDetails, useForm101Data, useCreateForm101, useUpdateForm101, useSendForm101ForSignature, useCheckForm101SignatureStatus, useCancelForm101SignatureRequest } from "../hooks";
import { Form101PreviewModal } from "./form101-preview-modal";
import { Form101EditModal } from "./form101-edit-modal";
import { Form101ResendModal } from "./form101-resend-modal";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { type Form101, type MaritalStatus } from "@prisma/client";

interface Form101ManagerProps {
  employeeId: string;
  employeeName: string;
  year?: number;
}

interface Form101Status {
  exists: boolean;
  status: 'draft' | 'sent' | 'signed' | 'expired';
  sentAt?: Date;
  signedAt?: Date;
  expiresAt?: Date;
  documentUrl?: string;
  signatureRequestId?: string;
}

export function Form101Manager({ employeeId, employeeName, year = new Date().getFullYear() }: Form101ManagerProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [showResendDialog, setShowResendDialog] = useState(false);

  const { employee, isLoading: isLoadingEmployee } = useEmployeeDetails(employeeId);
  const { form101, isLoading: isLoadingForm101, refetch } = useForm101Data(employeeId, year);
  const { createForm101, isCreating } = useCreateForm101();
  const { updateForm101, isUpdating } = useUpdateForm101();
  const { sendForSignature, isSending } = useSendForm101ForSignature();
  const { cancelSignatureRequest, isCancelling } = useCancelForm101SignatureRequest();
  const { signatureStatus, isLoading: isCheckingStatus, refetch: refetchStatus } = useCheckForm101SignatureStatus(form101?.id || "");

  const isLoading = isLoadingEmployee || isLoadingForm101 || isCreating || isUpdating || isSending || isCheckingStatus;
  const isSendingOrCancelling = isSending || isCancelling;

  // Determine form status
  const getFormStatus = (): Form101Status => {
    if (!form101) {
      return { exists: false, status: 'draft' };
    }

    if (form101.signedAt) {
      return {
        exists: true,
        status: 'signed',
        signedAt: form101.signedAt,
        documentUrl: form101.documentUrl || undefined
      };
    }

    if (form101.signatureRequestId) {
      const isExpired = form101.signatureRequestExpiresAt && form101.signatureRequestExpiresAt < new Date();

      return {
        exists: true,
        status: isExpired ? 'expired' : 'sent',
        sentAt: form101.signatureRequestSentAt || undefined,
        expiresAt: form101.signatureRequestExpiresAt || undefined,
        signatureRequestId: form101.signatureRequestId
      };
    }

    return {
      exists: true,
      status: 'draft'
    };
  };

  const formStatus = getFormStatus();

  // Auto-fill form data from employee details
  const getAutoFilledData = () => {
    if (!employee) return null;

    return {
      employeeId,
      taxYear: year,
      maritalStatus: 'SINGLE' as MaritalStatus,
      spouseWorks: false,
      childrenCount: 0,
      childrenUnder5: 0,
      childrenUnder18: 0,
      isMainEmployer: true,
      hasAdditionalIncome: false
    };
  };

  const handleCreateForm = async () => {
    const autoFilledData = getAutoFilledData();
    if (!autoFilledData) return;

    try {
      await createForm101(autoFilledData);
      toast.success("טופס 101 נוצר בהצלחה");
      refetch();
    } catch (error) {
      toast.error("שגיאה ביצירת טופס 101");
    }
  };

  const handleSendForSignature = async () => {
    if (!form101 || !employee?.email) return;

    try {
      await sendForSignature({
        form101Id: form101.id,
        employeeEmail: employee.email,
        employeePhone: employee.phone
      });
      toast.success("הטופס נשלח לחתימה בהצלחה");
      refetch();
      setShowResendDialog(false);
    } catch (error) {
      toast.error("שגיאה בשליחת הטופס לחתימה");
    }
  };

  const handleCancelAndResend = async () => {
    if (!form101 || !form101.signatureRequestId) return;

    try {
      // Cancel current request
      await cancelSignatureRequest({ form101Id: form101.id });
      // Send new request
      await handleSendForSignature();
      toast.success("הטופס נשלח מחדש בהצלחה");
      setShowResendDialog(false);
    } catch (error) {
      toast.error("שגיאה בשליחה מחדש של הטופס");
    }
  };

  const handleResend = () => {
    setShowResendDialog(true);
  };

  const renderStatusBadge = () => {
    switch (formStatus.status) {
      case 'signed':
        return <Badge variant="default" className="bg-green-600">נחתם</Badge>;
      case 'sent':
        return <Badge variant="secondary" className="bg-blue-600">ממתין לחתימה</Badge>;
      case 'expired':
        return <Badge variant="destructive">פג תוקף</Badge>;
      case 'draft':
        return <Badge variant="outline">טיוטה</Badge>;
    }
  };

  const renderActions = () => {
    if (!formStatus.exists) {
      return (
        <Button onClick={handleCreateForm} disabled={isLoading}>
          <FileText className="h-4 w-4 ml-2" />
          {isCreating ? "יוצר טופס..." : "צור טופס 101"}
        </Button>
      );
    }

    switch (formStatus.status) {
      case 'draft':
        return (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowEdit(true)} disabled={isLoading}>
              <Edit className="h-4 w-4 ml-2" />
              ערוך
            </Button>
            <Button variant="outline" onClick={() => setShowPreview(true)} disabled={isLoading}>
              <Eye className="h-4 w-4 ml-2" />
              תצוגה מקדימה
            </Button>
            <Button
              onClick={handleSendForSignature}
              disabled={isLoading || !employee?.email}
              title={!employee?.email ? "נדרשת כתובת דוא״ל של העובד" : undefined}
            >
              <Send className="h-4 w-4 ml-2" />
              {isSending ? "שולח..." : "שלח לחתימה"}
            </Button>
          </div>
        );

      case 'sent':
        return (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowPreview(true)} disabled={isLoading}>
              <Eye className="h-4 w-4 ml-2" />
              תצוגה מקדימה
            </Button>
            <Button variant="outline" onClick={handleResend} disabled={isLoading}>
              <RefreshCw className="h-4 w-4 ml-2" />
              שלח שוב
            </Button>
          </div>
        );

      case 'expired':
        return (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowEdit(true)} disabled={isLoading}>
              <Edit className="h-4 w-4 ml-2" />
              ערוך
            </Button>
            <Button onClick={handleResend} disabled={isLoading}>
              <RefreshCw className="h-4 w-4 ml-2" />
              שלח שוב
            </Button>
          </div>
        );

      case 'signed':
        return (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowPreview(true)} disabled={isLoading}>
              <Eye className="h-4 w-4 ml-2" />
              הצג טופס חתום
            </Button>
            {formStatus.documentUrl && (
              <Button variant="outline" asChild>
                <a href={formStatus.documentUrl} download className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">
                  <Download className="h-4 w-4 ml-2" />
                  הורד
                </a>
              </Button>
            )}
          </div>
        );
    }
  };

  if (isLoadingEmployee || isLoadingForm101) {
    return (
      <Card>
        <CardContent className="p-6">
          <Skeleton className="h-8 w-48 mb-4" />
          <Skeleton className="h-4 w-32" />
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5" />
              <CardTitle>טופס 101 - {year}</CardTitle>
              {renderStatusBadge()}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status Information */}
            {formStatus.status === 'sent' && formStatus.sentAt && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>נשלח לחתימה ב-{format(formStatus.sentAt, 'dd/MM/yyyy HH:mm', { locale: he })}</span>
                {formStatus.expiresAt && (
                  <span className="text-orange-600">
                    • יפוג ב-{format(formStatus.expiresAt, 'dd/MM/yyyy', { locale: he })}
                  </span>
                )}
              </div>
            )}

            {formStatus.status === 'signed' && formStatus.signedAt && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>נחתם ב-{format(formStatus.signedAt, 'dd/MM/yyyy HH:mm', { locale: he })}</span>
              </div>
            )}

            {formStatus.status === 'expired' && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span>פג תוקף הבקשה לחתימה. יש לשלוח שוב.</span>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end">
              {renderActions()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Modal */}
      {form101 && (
        <Form101PreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          form101Data={form101}
          employeeName={employeeName}
        />
      )}

      {/* Edit Modal */}
      {form101 && (
        <Form101EditModal
          isOpen={showEdit}
          onClose={() => setShowEdit(false)}
          form101Data={form101}
          onSave={async (data) => {
            await updateForm101({ id: form101.id, ...data });
            refetch();
            setShowEdit(false);
          }}
        />
      )}

      {/* Resend Modal */}
      {form101 && (
        <Form101ResendModal
          isOpen={showResendDialog}
          onClose={() => setShowResendDialog(false)}
          onResend={handleSendForSignature}
          onCancelAndResend={handleCancelAndResend}
          form101Data={form101}
          isLoading={isSendingOrCancelling}
          isActive={formStatus.status === 'sent'}
        />
      )}
    </>
  );
}