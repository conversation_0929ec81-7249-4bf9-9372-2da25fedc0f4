import { dashboardRouter } from "@/server/api/routers/dashboard";
import { employerRouter } from "@/server/api/routers/employer";
import { employeeRouter } from "@/server/api/routers/employee";
import { userRouter } from "@/server/api/routers/user";
import { reportRouter } from "@/server/api/routers/report";
import { auditLogRouter } from "@/server/api/routers/auditLog";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { tenantRouter } from "@/server/api/routers/tenant";
import { integrationRouter } from "@/server/api/routers/integration";
import { documentSettingsRouter } from "@/server/api/routers/documentSettings";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
        dashboard: dashboardRouter,
        employer: employerRouter,
        employee: employeeRouter,
        user: userRouter,
        report: reportRouter,
        auditLog: auditLogRouter,
        documentSettings: documentSettingsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.user.all();
 *       ^? User[]
 */
export const createCaller = createCallerFactory(appRouter);
