import { db } from "@/server/db";
import type { AuditAction } from "@prisma/client";

// SMS gateway details, preferably stored in environment variables
export const SMS_GATEWAY_URL =
  "https://uapi.inforu.co.il/inforufrontend/WebInterface/SendMessageByNumber.aspx";
export const SMS_USERNAME = "wavesmartflow";
export const SMS_PASSWORD = "dba5ad1f-5084-4ae6-9c28-a3e5f31d75f7";
export const SMS_SENDER = "WaveSmart";

// Utility function to check if URL would be too long
const isUrlTooLong = (phone: string, message: string): boolean => {
  const encodedURL = encodeURI(
    `${SMS_GATEWAY_URL}?UserName=${SMS_USERNAME}&Password=${SMS_PASSWORD}&SenderCellNumber=${SMS_SENDER}&CellNumber=${phone}&MessageString=${message}`
  );
  return encodedURL.length > 1500; // URLs longer than 1500 chars will use POST
};

// Helper function to create audit log for SMS activities
async function createSmsAuditLog(
  tenantId: string,
  action: AuditAction,
  smsLogId: string,
  details: Record<string, any>,
  userId?: string,
  userEmail?: string
) {
  try {
    await db.auditLog.create({
      data: {
        tenantId,
        modelName: "SmsLog",
        recordId: smsLogId,
        action,
        newValues: details,
        userId,
        userEmail,
      },
    });
  } catch (error) {
    console.error("Failed to create audit log for SMS:", error);
  }
}

export async function sendMessage(
  phone: string,
  message: string,
  employeeId: string,
  tenantId: string,
  userId?: string,
  userEmail?: string
): Promise<boolean> {
  console.log("sendMessage called with employeeId:", employeeId);

  if (!employeeId) {
    console.error("No employee ID provided");
    return false;
  }

  if (!tenantId) {
    console.error("No tenant ID provided");
    return false;
  }

  try {
    let response: Response;
    let body: string;

    // Check if message is too long for GET request
    const usePost = isUrlTooLong(phone, message);

    if (usePost) {
      console.log("sendMessage: Message is long, using POST method");

      // Prepare the form data
      const formData = new URLSearchParams();
      formData.append("UserName", SMS_USERNAME);
      formData.append("Password", SMS_PASSWORD);
      formData.append("SenderCellNumber", SMS_SENDER);
      formData.append("CellNumber", phone);
      formData.append("MessageString", message);

      // Use POST method with form data
      response = await fetch(SMS_GATEWAY_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData.toString(),
      });

      console.log("sendMessage: POST request sent to SMS gateway");
    } else {
      // For shorter messages, use the original GET method
      const encodedURL = encodeURI(
        `${SMS_GATEWAY_URL}?UserName=${SMS_USERNAME}&Password=${SMS_PASSWORD}&SenderCellNumber=${SMS_SENDER}&CellNumber=${phone}&MessageString=${message}`
      );

      console.log("sendMessage: Attempting to send SMS using URL:", encodedURL);

      // Use GET method
      response = await fetch(encodedURL, { method: "GET" });
    }

    // Log detailed response information
    console.log(`sendMessage: API Response details for ${phone}:`);
    console.log(`- Status: ${response.status}`);
    console.log(`- Status Text: ${response.statusText}`);
    console.log(`- Response Type: ${response.type}`);
    console.log(`- Response URL: ${response.url}`);
    console.log(`- Headers:`);

    // Log all response headers
    response.headers.forEach((value: string, name: string) => {
      console.log(`  ${name}: ${value}`);
    });

    body = await response.text();
    console.log("sendMessage: Response body from SMS gateway:", body);
    console.log("sendMessage: Sending SMS to employee ID:", employeeId);

    // Log the SMS in the database
    console.log(
      "sendMessage: Attempting to create SMS record for employeeId:",
      employeeId
    );

    // Construct detailed status message for database
    const statusDetails = {
      gatewayResponse: body,
      httpStatus: response.status,
      httpStatusText: response.statusText,
      success: response.ok && body === "1",
    };

    console.log(
      "sendMessage: Status details to be saved:",
      JSON.stringify(statusDetails)
    );

    // Log SMS to database
    const smsRecord = await db.smsLog.create({
      data: {
        tenantId: tenantId,
        employeeId: employeeId,
        phone: phone,
        message: message,
        status: response.ok && body === "1",
        failureReason: body !== "1" ? `API returned: ${body}` : null,
        gatewayResponse: body,
        httpStatus: response.status,
      },
    });
    console.log("sendMessage: SMS Record Created:", smsRecord);

    // Create audit log for successful SMS
    await createSmsAuditLog(
      tenantId,
      "CREATE" as AuditAction,
      smsRecord.id,
      {
        phone,
        employeeId,
        messageLength: message.length,
        status: smsRecord.status,
        method: usePost ? "POST" : "GET",
        gatewayResponse: body,
      },
      userId,
      userEmail
    );

    return response.ok && body === "1";
  } catch (error) {
    // Enhanced error logging
    console.error("sendMessage: Error in sendMessage function:");
    console.error(`- Error Message: ${(error as Error).message}`);
    console.error(`- Error Name: ${(error as Error).name}`);
    console.error(`- Error Stack:`, (error as Error).stack);

    // Try to get any fetch-specific error details
    if ((error as any).type) {
      console.error(`- Fetch Error Type: ${(error as any).type}`);
    }

    if ((error as any).code) {
      console.error(`- Error Code: ${(error as any).code}`);
    }

    // Log the SMS failure in the database with detailed error
    try {
      const failedSmsRecord = await db.smsLog.create({
        data: {
          tenantId: tenantId,
          employeeId: employeeId,
          phone: phone,
          message: message,
          status: false,
          failureReason: `Error: ${(error as Error).message || "Unknown error"}`,
        },
      });
      console.log("sendMessage: Error logged to database");

      // Create audit log for failed SMS
      await createSmsAuditLog(
        tenantId,
        "CREATE" as AuditAction,
        failedSmsRecord.id,
        {
          phone,
          employeeId,
          messageLength: message.length,
          status: false,
          error: (error as Error).message || "Unknown error",
          errorType: (error as any).type || "Unknown",
        },
        userId,
        userEmail
      );
    } catch (dbError) {
      console.error("sendMessage: Failed to log error to database:", dbError);
    }

    return false;
  }
}

// Helper function to send SMS with template
export async function sendTemplatedSMS(
  phone: string,
  template: string,
  variables: Record<string, string>,
  employeeId: string,
  tenantId: string,
  userId?: string,
  userEmail?: string
): Promise<boolean> {
  // Replace variables in template
  let message = template;
  for (const [key, value] of Object.entries(variables)) {
    message = message.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  
  return sendMessage(phone, message, employeeId, tenantId, userId, userEmail);
}

// Helper function to validate Israeli phone number
export function validateIsraeliPhoneNumber(phone: string): boolean {
  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Israeli phone numbers can be:
  // - 9 digits starting with 5 (mobile)
  // - 9 digits starting with 2,3,4,7,8,9 (landline)
  // - With country code: 972 followed by the above without the leading 0
  
  // Check if it's a valid Israeli mobile number
  if (/^05\d{8}$/.test(cleanPhone)) {
    return true;
  }
  
  // Check if it's a valid Israeli landline
  if (/^0[234789]\d{7}$/.test(cleanPhone)) {
    return true;
  }
  
  // Check with country code (972)
  if (/^972[234789]\d{7}$/.test(cleanPhone) || /^9725\d{8}$/.test(cleanPhone)) {
    return true;
  }
  
  return false;
}

// Helper function to format phone number for SMS gateway
export function formatPhoneForSMS(phone: string): string {
  // Remove any non-digit characters
  let cleanPhone = phone.replace(/\D/g, '');
  
  // If it starts with 0, remove it
  if (cleanPhone.startsWith('0')) {
    cleanPhone = cleanPhone.substring(1);
  }
  
  // If it doesn't start with country code, add it
  if (!cleanPhone.startsWith('972')) {
    cleanPhone = '972' + cleanPhone;
  }
  
  return cleanPhone;
}

// Helper function to send bulk SMS
export async function sendBulkSMS(
  recipients: Array<{ phone: string; employeeId: string; variables?: Record<string, string> }>,
  template: string,
  tenantId: string,
  userId?: string,
  userEmail?: string
): Promise<Array<{ employeeId: string; success: boolean; error?: string }>> {
  const results = [];
  
  // Create audit log for bulk SMS operation start
  const bulkOperationId = crypto.randomUUID();
  await createSmsAuditLog(
    tenantId,
    "CREATE" as AuditAction,
    bulkOperationId,
    {
      operation: "BULK_SMS_START",
      recipientCount: recipients.length,
      template: template.substring(0, 100) + (template.length > 100 ? "..." : ""),
    },
    userId,
    userEmail
  );
  
  for (const recipient of recipients) {
    try {
      let message = template;
      
      // Replace variables if provided
      if (recipient.variables) {
        for (const [key, value] of Object.entries(recipient.variables)) {
          message = message.replace(new RegExp(`{{${key}}}`, 'g'), value);
        }
      }
      
      const success = await sendMessage(
        recipient.phone,
        message,
        recipient.employeeId,
        tenantId,
        userId,
        userEmail
      );
      
      results.push({
        employeeId: recipient.employeeId,
        success,
      });
    } catch (error) {
      results.push({
        employeeId: recipient.employeeId,
        success: false,
        error: (error as Error).message,
      });
    }
  }
  
  // Create audit log for bulk SMS operation completion
  const successCount = results.filter(r => r.success).length;
  await createSmsAuditLog(
    tenantId,
    "UPDATE" as AuditAction,
    bulkOperationId,
    {
      operation: "BULK_SMS_COMPLETE",
      totalRecipients: recipients.length,
      successCount,
      failureCount: recipients.length - successCount,
      results: results,
    },
    userId,
    userEmail
  );
  
  return results;
}

// SMS templates
export const SMS_TEMPLATES = {
  PAYSLIP_READY: "שלום {{firstName}}, תלוש השכר שלך לחודש {{month}}/{{year}} מוכן לצפייה במערכת.",
  DOCUMENT_UPLOADED: "שלום {{firstName}}, מסמך חדש '{{documentName}}' הועלה לחשבונך במערכת.",
  VISA_EXPIRY_WARNING: "שלום {{firstName}}, שים לב שהאשרה שלך תפוג בתאריך {{expiryDate}}. אנא פנה למחלקת משאבי אנוש.",
  LEAVE_APPROVED: "שלום {{firstName}}, בקשת החופשה שלך מתאריך {{startDate}} עד {{endDate}} אושרה.",
  GENERAL_NOTIFICATION: "{{message}}",
};
