import { type Form101 } from "@prisma/client";

export interface SignatureRequest {
  id: string;
  status: 'pending' | 'signed' | 'expired' | 'cancelled';
  documentUrl?: string;
  signedAt?: Date;
  expiresAt: Date;
}

export interface SignatureServiceConfig {
  apiKey: string;
  apiUrl: string;
}

export class SignatureService {
  private config: SignatureServiceConfig;

  constructor(config: SignatureServiceConfig) {
    this.config = config;
  }

  async sendForSignature(params: {
    form101: Form101;
    employeeEmail: string;
    employeePhone?: string;
    documentData: Buffer;
  }): Promise<SignatureRequest> {
    // TODO: Implement actual signature service integration
    // For now, return mock data
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    return {
      id: `sig_${Math.random().toString(36).substring(7)}`,
      status: 'pending',
      expiresAt
    };
  }

  async checkStatus(signatureRequestId: string): Promise<SignatureRequest> {
    // TODO: Implement actual signature service integration
    // For now, return mock data
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    return {
      id: signatureRequestId,
      status: 'pending',
      expiresAt
    };
  }

  async cancelRequest(signatureRequestId: string): Promise<void> {
    // TODO: Implement actual signature service integration
  }
}

// Create singleton instance
let signatureService: SignatureService | null = null;

export function getSignatureService(): SignatureService {
  if (!signatureService) {
    // TODO: Get config from environment variables
    signatureService = new SignatureService({
      apiKey: process.env.SIGNATURE_SERVICE_API_KEY || 'mock_key',
      apiUrl: process.env.SIGNATURE_SERVICE_API_URL || 'https://api.signature-service.example.com'
    });
  }
  return signatureService;
} 