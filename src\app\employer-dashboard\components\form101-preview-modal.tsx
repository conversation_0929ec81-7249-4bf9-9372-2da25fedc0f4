"use client";

import React from "react";
import { Di<PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { type Form101 } from "@prisma/client";

interface Form101PreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  form101Data: Form101;
  employeeName: string;
}

export function Form101PreviewModal({ isOpen, onClose, form101Data, employeeName }: Form101PreviewModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>טופס 101 - {employeeName}</DialogTitle>
        </DialogHeader>
        <div className="p-4">
          {/* TODO: Implement form preview */}
          <p>תצוגה מקדימה של הטופס תהיה זמינה בקרוב</p>
        </div>
      </DialogContent>
    </Dialog>
  );
} 