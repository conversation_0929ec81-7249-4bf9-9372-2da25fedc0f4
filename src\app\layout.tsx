import "@/styles/globals.css";

import type { <PERSON>ada<PERSON> } from "next";
// import { He<PERSON><PERSON> } from "next/font/google";
import { SessionProvider } from "@/components/SessionProvider";

import { TRPCReactProvider } from "@/trpc/react";

export const metadata: Metadata = {
	title: "SMARTCHI",
	description: "Israeli payroll management system",
	icons: [{ rel: "icon", url: "/favicon.ico" }],
};

// const heebo = Heebo({
// 	subsets: ["hebrew"],
// 	variable: "--font-heebo",
// });

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="he" dir="rtl" suppressHydrationWarning>
			<body className="to-background text-foreground relative ">
				<TRPCReactProvider>
					<SessionProvider>{children}</SessionProvider>
				</TRPCReactProvider>
			</body>
		</html>
	);
}
