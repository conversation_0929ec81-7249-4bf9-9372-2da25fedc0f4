generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id                 String                      @id @default(uuid()) @db.Uuid
  name               String                      @unique
  plan               String?
  createdAt          DateTime                    @default(now())
  updatedAt          DateTime                    @updatedAt
  alerts             Alert[]
  auditLogs          AuditLog[]
  bankAccounts       BankAccount[]
  departments        Department[]
  documentSettings   DocumentSettings?
  documents          Document[]
  employees          Employee[]
  employers          Employer[]
  form101s           Form101[]
  form102s           Form102[]
  form106s           Form106[]
  form126s           Form126[]
  importExports      ImportExportHistory[]
  leaveRecords       LeaveRecord[]
  insurance          NationalInsuranceRecord[]
  payslips           Payslip[]
  providentFunds     ProvidentFund[]
  contributions      ProvidentFundContribution[]
  reports            Report[]
  salaryRecords      SalaryRecord[]
  salaryTransactions SalaryTransaction[]
  smsLogs            SmsLog[]
  users              User[]
}

model User {
  id                 String                @id @default(uuid()) @db.Uuid
  tenantId           String                @db.Uuid
  email              String
  name               String?
  role               Role
  password           String
  isActive           Boolean               @default(true)
  preferences        Json?
  mustResetPassword  Boolean               @default(false)
  employerId         String?               @db.Uuid
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  alerts             Alert[]
  auditLogs          AuditLog[]
  importExports      ImportExportHistory[]
  reports            Report[]
  salaryTransactions SalaryTransaction[]
  employer           Employer?             @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant             Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, email])
  @@index([tenantId])
  @@index([employerId])
}

model Alert {
  id         String         @id @default(uuid()) @db.Uuid
  tenantId   String         @db.Uuid
  userId     String?        @db.Uuid
  employeeId String?        @db.Uuid
  type       AlertType      @default(INFO)
  category   AlertCategory?
  message    String
  context    Json?
  isRead     Boolean        @default(false)
  readAt     DateTime?
  dueDate    DateTime?
  severity   String?
  isResolved Boolean        @default(false)
  resolvedAt DateTime?
  createdAt  DateTime       @default(now())
  employee   Employee?      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant     Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user       User?          @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([userId])
  @@index([employeeId])
  @@index([category])
  @@index([isResolved])
}

model AuditLog {
  id        String      @id @default(uuid()) @db.Uuid
  tenantId  String      @db.Uuid
  modelName String
  recordId  String?     @db.Uuid
  action    AuditAction
  oldValues Json?
  newValues Json?
  userId    String?     @db.Uuid
  userEmail String?
  ipAddress String?
  timestamp DateTime    @default(now())
  tenant    Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User?       @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([modelName, recordId])
  @@index([userId])
}

model Employer {
  id                String        @id @default(uuid()) @db.Uuid
  tenantId          String        @db.Uuid
  name              String
  identifier        String?
  taxId             String?
  niNumber          String?
  companyId         String?
  industry          Sector?
  registrationDate  DateTime?
  accountManager    String?
  payrollDay        Int?
  address           Json?
  contact           Json?
  profilePictureUrl String?
  profilePictureKey String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  bankAccounts      BankAccount[]
  departments       Department[]
  documents         Document[]
  employees         Employee[]
  tenant            Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  forms102          Form102[]
  forms126          Form126[]
  reports           Report[]
  users             User[]

  @@unique([tenantId, name])
  @@unique([tenantId, companyId])
  @@index([tenantId])
  @@index([profilePictureUrl])
}

model Department {
  id                 String              @id @default(uuid()) @db.Uuid
  tenantId           String              @db.Uuid
  employerId         String              @db.Uuid
  name               String
  code               String?
  description        String?
  isActive           Boolean             @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  employer           Employer            @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant             Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employees          Employee[]
  payslipItems       PayslipItem[]
  salaryTransactions SalaryTransaction[]

  @@unique([employerId, name])
  @@unique([employerId, code])
  @@index([tenantId])
  @@index([employerId])
}

model BankAccount {
  id            String       @id @default(uuid()) @db.Uuid
  tenantId      String       @db.Uuid
  bankName      String
  branchCode    String?
  accountNumber String
  bankCode      Int?
  branchNumber  Int?
  currency      Currency     @default(ILS)
  accountType   AccountType?
  isPrimary     Boolean      @default(false)
  percentage    Decimal?     @db.Decimal(5, 2)
  employeeId    String?      @db.Uuid
  employerId    String?      @db.Uuid
  effectiveFrom DateTime?
  effectiveTo   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  employee      Employee?    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  employer      Employer?    @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant        Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([employerId])
}

model Employee {
  id                  String                      @id @default(uuid()) @db.Uuid
  tenantId            String                      @db.Uuid
  employerId          String                      @db.Uuid
  departmentId        String?                     @db.Uuid
  firstName           String
  lastName            String
  nationalId          String
  status              EmployeeStatus              @default(ACTIVE)
  birthDate           DateTime?
  address             Json?
  contact             Json?
  startDate           DateTime
  endDate             DateTime?
  profilePictureUrl   String?
  profilePictureKey   String?
  isForeign           Boolean                     @default(false)
  country             String?
  sector              Sector?
  agreementType       AgreementType?
  isResidentForNI     Boolean                     @default(true)
  visaNumber          String?
  visaExpiry          DateTime?
  visaType            String?
  baseSalary          Decimal?                    @db.Decimal(12, 2)
  travelAllowance     Decimal?                    @db.Decimal(12, 2)
  primaryAssignment   String?
  secondaryAssignment String?
  isControllingOwner  Boolean?
  isUnsupervised      Boolean?
  terminationReason   String?
  assignments         Json?
  createdAt           DateTime                    @default(now())
  updatedAt           DateTime                    @updatedAt
  alerts              Alert[]
  bankAccounts        BankAccount[]
  documents           Document[]
  department          Department?                 @relation(fields: [departmentId], references: [id])
  employer            Employer                    @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant              Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  form101s            Form101[]
  form106s            Form106[]
  leaveRecords        LeaveRecord[]
  insurance           NationalInsuranceRecord[]
  payslips            Payslip[]
  provident           ProvidentFundContribution[]
  salaryRecords       SalaryRecord[]
  salaryTransactions  SalaryTransaction[]
  smsLogs             SmsLog[]

  @@unique([tenantId, nationalId])
  @@index([tenantId])
  @@index([employerId])
  @@index([nationalId])
  @@index([tenantId, employerId])
  @@index([profilePictureUrl])
  @@index([isForeign])
  @@index([sector])
  @@index([visaExpiry])
  @@index([departmentId])
}

model SalaryRecord {
  id                   String        @id @default(uuid()) @db.Uuid
  tenantId             String        @db.Uuid
  employeeId           String        @db.Uuid
  basis                Basis         @default(MONTHLY)
  currency             Currency      @default(ILS)
  payFrequency         PayFrequency  @default(MONTHLY)
  amount               Decimal       @db.Decimal(12, 2)
  effectiveFrom        DateTime
  effectiveTo          DateTime?
  positionType         PositionType?
  hourlyRate           Decimal?      @db.Decimal(12, 2)
  dailyRate            Decimal?      @db.Decimal(12, 2)
  workedHours          Decimal?      @db.Decimal(8, 2)
  workedDays           Decimal?      @db.Decimal(5, 2)
  standardMonthlyHours Decimal?      @db.Decimal(8, 2)
  standardDailyHours   Decimal?      @db.Decimal(5, 2)
  standardMonthDays    Int?
  workDaysPerWeek      Int?
  positionPercentage   Int?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  employee             Employee      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant               Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
}

model Payslip {
  id                String                      @id @default(uuid()) @db.Uuid
  tenantId          String                      @db.Uuid
  employeeId        String                      @db.Uuid
  year              Int
  month             Int
  periodStart       DateTime?
  periodEnd         DateTime?
  status            PayslipStatus               @default(DRAFT)
  grossPay          Decimal                     @db.Decimal(12, 2)
  netPay            Decimal                     @db.Decimal(12, 2)
  taxDeducted       Decimal                     @db.Decimal(12, 2)
  insuranceDeducted Decimal                     @db.Decimal(12, 2)
  otherDeductions   Decimal?                    @db.Decimal(12, 2)
  allowances        Decimal?                    @db.Decimal(12, 2)
  currency          Currency                    @default(ILS)
  healthInsurance   Decimal?                    @db.Decimal(12, 2)
  pensionEmployee   Decimal?                    @db.Decimal(12, 2)
  pensionEmployer   Decimal?                    @db.Decimal(12, 2)
  severancePay      Decimal?                    @db.Decimal(12, 2)
  netDeposit        Decimal?                    @db.Decimal(12, 2)
  breakdown         Json?
  issuedAt          DateTime                    @default(now())
  documentRelations DocumentToPayslip[]
  employee          Employee                    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant            Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             PayslipItem[]
  contributions     ProvidentFundContribution[]

  @@unique([employeeId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year, month])
  @@index([status])
}

model PayslipItem {
  id           String          @id @default(uuid()) @db.Uuid
  payslipId    String          @db.Uuid
  description  String
  amount       Decimal         @db.Decimal(12, 2)
  type         PayslipItemType
  kod          PayslipItemKod?
  rate         Decimal?        @db.Decimal(12, 2)
  units        Decimal?        @db.Decimal(12, 2)
  percentage   Decimal?        @db.Decimal(5, 2)
  isDebit      Boolean         @default(false)
  isInfo       Boolean         @default(false)
  departmentId String?         @db.Uuid
  note         String?
  department   Department?     @relation(fields: [departmentId], references: [id])
  payslip      Payslip         @relation(fields: [payslipId], references: [id], onDelete: Cascade)

  @@index([payslipId])
  @@index([kod])
  @@index([type])
  @@index([departmentId])
}

model SalaryTransaction {
  id            String          @id @default(uuid()) @db.Uuid
  tenantId      String          @db.Uuid
  employeeId    String          @db.Uuid
  periodMonth   Int
  periodYear    Int
  componentCode PayslipItemKod?
  description   String?
  quantity      Decimal?        @db.Decimal(12, 2)
  rate          Decimal?        @db.Decimal(12, 2)
  percentage    Decimal?        @db.Decimal(5, 2)
  amount        Decimal?        @db.Decimal(12, 2)
  fromDate      DateTime?
  toDate        DateTime?
  departmentId  String?         @db.Uuid
  source        String?
  isGrossedUp   Boolean         @default(false)
  isProcessed   Boolean         @default(false)
  userId        String?         @db.Uuid
  note          String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  department    Department?     @relation(fields: [departmentId], references: [id])
  employee      Employee        @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant        Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user          User?           @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([employeeId])
  @@index([periodYear, periodMonth])
  @@index([isProcessed])
  @@index([departmentId])
  @@index([userId])
}

model ProvidentFund {
  id            String                      @id @default(uuid()) @db.Uuid
  tenantId      String                      @db.Uuid
  name          String
  fundNumber    String
  fundType      FundType                    @default(PENSION)
  tenant        Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  contributions ProvidentFundContribution[]

  @@unique([tenantId, fundNumber])
  @@index([tenantId])
}

model ProvidentFundContribution {
  id             String        @id @default(uuid()) @db.Uuid
  tenantId       String        @db.Uuid
  employeeId     String        @db.Uuid
  fundId         String        @db.Uuid
  payslipId      String?       @db.Uuid
  year           Int
  month          Int
  employeeAmount Decimal       @db.Decimal(12, 2)
  employerAmount Decimal       @db.Decimal(12, 2)
  severancePay   Decimal       @db.Decimal(12, 2)
  totalAmount    Decimal       @db.Decimal(12, 2)
  capApplied     Boolean       @default(false)
  fundName       String?
  employee       Employee      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  fund           ProvidentFund @relation(fields: [fundId], references: [id], onDelete: Cascade)
  payslip        Payslip?      @relation(fields: [payslipId], references: [id], onDelete: Cascade)
  tenant         Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, fundId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([fundId])
  @@index([year, month])
  @@index([tenantId, year, month])
  @@index([payslipId])
}

model NationalInsuranceRecord {
  id         String   @id @default(uuid()) @db.Uuid
  tenantId   String   @db.Uuid
  employeeId String   @db.Uuid
  year       Int
  month      Int
  employeeNI Decimal  @db.Decimal(12, 2)
  employerNI Decimal  @db.Decimal(12, 2)
  healthNI   Decimal  @db.Decimal(12, 2)
  totalNI    Decimal  @db.Decimal(12, 2)
  earnings   Decimal  @db.Decimal(12, 2)
  employee   Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant     Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year, month])
}

model LeaveRecord {
  id              String    @id @default(uuid()) @db.Uuid
  tenantId        String    @db.Uuid
  employeeId      String    @db.Uuid
  leaveType       LeaveType
  leaveTypeCode   Int?
  startDate       DateTime
  endDate         DateTime
  entitlement     Decimal?  @db.Decimal(8, 2)
  taken           Decimal?  @db.Decimal(8, 2)
  previousBalance Decimal?  @db.Decimal(8, 2)
  notes           String?
  autoCalculated  Boolean?
  year            Int
  month           Int
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  employee        Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant          Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([leaveType])
  @@index([startDate, endDate])
  @@index([year, month])
}

model Form101 {
  id                        String         @id @default(uuid()) @db.Uuid
  tenantId                  String         @db.Uuid
  employeeId                String         @db.Uuid
  taxYear                   Int?
  maritalStatus             MaritalStatus?
  spouseWorks               Boolean        @default(false)
  childrenCount             Int            @default(0)
  childrenUnder5            Int            @default(0)
  childrenUnder18           Int            @default(0)
  additionalCreditPoints    Decimal?       @db.Decimal(4, 2)
  isMainEmployer            Boolean        @default(true)
  hasAdditionalIncome       Boolean        @default(false)
  taxCoordinationNumber     String?
  exemptionPercentage       Decimal?       @db.Decimal(5, 2)
  overrideTaxRate           Decimal?       @db.Decimal(5, 2)
  signatureRequestId        String?
  signatureRequestSentAt    DateTime?
  signatureRequestExpiresAt DateTime?
  signedAt                  DateTime?
  documentUrl               String?
  createdAt                 DateTime       @default(now())
  updatedAt                 DateTime       @updatedAt
  employee                  Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant                    Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, taxYear])
  @@index([tenantId])
  @@index([employeeId])
  @@index([taxYear])
}

model Form102 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employerId  String    @db.Uuid
  year        Int
  month       Int
  data        Json
  submittedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employer    Employer  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employerId, year, month])
  @@index([tenantId])
  @@index([employerId])
  @@index([year, month])
}

model Form106 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employeeId  String    @db.Uuid
  year        Int
  data        Json
  generatedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employee    Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, year])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year])
}

model Form126 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employerId  String    @db.Uuid
  year        Int
  data        Json
  submittedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employer    Employer  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employerId, year])
  @@index([tenantId])
  @@index([employerId])
  @@index([year])
}

model Report {
  id          String     @id @default(uuid()) @db.Uuid
  tenantId    String     @db.Uuid
  employerId  String?    @db.Uuid
  type        ReportType
  year        Int?
  month       Int?
  parameters  Json?
  filePath    String?
  generatedBy String?    @db.Uuid
  generatedAt DateTime   @default(now())
  version     Int        @default(1)
  employer    Employer?  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  generator   User?      @relation(fields: [generatedBy], references: [id])
  tenant      Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employerId])
  @@index([year, month])
  @@index([type])
}

model Document {
  id               String              @id @default(uuid()) @db.Uuid
  updatedAt        DateTime            @updatedAt
  tenantId         String              @db.Uuid
  employerId       String?             @db.Uuid
  employeeId       String?             @db.Uuid
  category         String?
  fileName         String
  title            String?
  description      String?
  fileSize         Int
  mimeType         String
  uploadedBy       String              @db.Uuid
  uploadedAt       DateTime            @default(now())
  url              String?
  s3Key            String?
  referenceModel   String?
  referenceId      String?             @db.Uuid
  metadata         Json?
  fileType         String?
  employee         Employee?           @relation(fields: [employeeId], references: [id])
  employer         Employer?           @relation(fields: [employerId], references: [id])
  tenant           Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  payslipRelations DocumentToPayslip[]
  form102s         Form102[]
  form106s         Form106[]
  form126s         Form126[]

  @@index([tenantId])
  @@index([employerId])
  @@index([employeeId])
  @@index([category])
  @@index([uploadedBy])
}

model DocumentToPayslip {
  documentId String   @db.Uuid
  payslipId  String   @db.Uuid
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  payslip    Payslip  @relation(fields: [payslipId], references: [id], onDelete: Cascade)

  @@id([documentId, payslipId])
}

model ImportExportHistory {
  id          String         @id @default(uuid()) @db.Uuid
  tenantId    String         @db.Uuid
  userId      String?        @db.Uuid
  action      TransferAction
  entity      String
  fileName    String?
  recordCount Int?
  status      String
  details     Json?
  startedAt   DateTime       @default(now())
  finishedAt  DateTime?
  tenant      Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User?          @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([userId])
  @@index([action, entity])
  @@index([status])
  @@index([startedAt])
}

model DocumentSettings {
  tenantId        String @id @db.Uuid
  allowedCategories Json
  tenant          Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

model SmsLog {
  id              String   @id @default(uuid()) @db.Uuid
  tenantId        String   @db.Uuid
  employeeId      String   @db.Uuid
  phone           String
  message         String
  status          Boolean
  failureReason   String?
  gatewayResponse String?
  httpStatus      Int?
  sentAt          DateTime @default(now())
  employee        Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant          Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([sentAt])
  @@index([status])
}

/// תפקידי משתמשים במערכת
enum Role {
  OWNER
  ADMIN
  ACCOUNTANT
  HR
  EMPLOYEE
}

enum Currency {
  ILS
  USD
  EUR
  OTHER
}

enum PayFrequency {
  MONTHLY
  BI_WEEKLY
  WEEKLY
}

enum Basis {
  MONTHLY
  HOURLY
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
}

enum AlertType {
  INFO
  WARNING
  CRITICAL
}

enum AlertCategory {
  VISA_EXPIRATION
  MISSING_FORM101
  MISSING_DOCUMENTS
  OVERTIME_LIMIT
  DEPOSIT_COMPLIANCE
  UNUSUAL_DEDUCTION
  GENERAL
}

enum EmployeeStatus {
  ACTIVE
  TERMINATED
  SUSPENDED
}

enum FundType {
  PENSION
  STUDY
  MANAGERS_INS
  COMPENSATION
  DEPOSIT
  HISHTALMUT
  UNION
}

enum PayslipItemType {
  EARNING
  DEDUCTION
  EMPLOYER_CONTRIB
  REIMBURSEMENT
}

/// ------------------------------------------------------------
/// Enum: PayslipItemKod
/// ערכי @map הם הערכים האמיתיים בבסיס-הנתונים (VARCHAR)
/// השמות עצמם צריכים להתחיל באות, לכן מוסיפים K_
/// ------------------------------------------------------------
enum PayslipItemKod {
  K_1000        @map("1000")
  K_1001        @map("1001")
  K_1002        @map("1002")
  K_1003        @map("1003")
  K_1004        @map("1004")
  K_1005        @map("1005")
  K_1008        @map("1008")
  K_1010        @map("1010")
  K_1011        @map("1011")
  K_1020        @map("1020")
  K_1021        @map("1021")
  K_1022        @map("1022")
  K_1063        @map("1063")
  K_1077        @map("1077")
  K_1089        @map("1089")
  K_1090        @map("1090")
  K_1091        @map("1091")
  K_0100        @map("0100")
  K_0102        @map("0102")
  K_0113        @map("0113")
  K_0114        @map("0114")
  K_0120        @map("0120")
  K_0131        @map("0131")
  K_0170        @map("0170")
  K_0191        @map("0191")
  K_0192        @map("0192")
  K_0193        @map("0193")
  K_0250        @map("0250")
  K_0312        @map("0312")
  K_0313        @map("0313")
  K_0400        @map("0400")
  K_0500        @map("0500")
  K_0900        @map("0900")
  K_0121        @map("0121")
  K_GILUM       @map("GILUM")
  K_TAX         @map("TAX")
  K_NI_EMP      @map("NI_EMP")
  K_NI_HEALTH   @map("NI_HEALTH")
  K_PENSION_EMP @map("PENSION_EMP")
  K_UNION       @map("UNION")
}

enum TransferAction {
  IMPORT
  EXPORT
}

enum AccountType {
  CHECKING
  SAVINGS
  CREDIT
  LOAN
}

enum ReportType {
  FORM_102
  PAYROLL_SUMMARY
  TAX_REPORT
  EMPLOYEE_OVERVIEW
}

enum PositionType {
  MONTHLY
  HOURLY
  DAILY
  FINAL_PAY
}

enum LeaveType {
  VACATION
  SICK
  UNPAID
  MATERNITY
  MILITARY
  INTER_VISA
  OTHER
}

enum AgreementType {
  PERSONAL
  COLLECTIVE
  TEMPLATE
}

enum Sector {
  AGRICULTURE
  CONSTRUCTION
  CAREGIVING
  INDUSTRY
  HOSPITALITY
  TECHNOLOGY
  OTHER
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
}

enum PayslipStatus {
  DRAFT
  CALCULATED
  APPROVED
  PAID
  CANCELLED
}
