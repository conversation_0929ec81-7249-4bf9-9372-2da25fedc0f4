"use client";

import { redirect } from "next/navigation";
import { useSession } from "next-auth/react";
import { type ReactNode } from "react";
import { Home, Users, Receipt, FileText, Building, Settings } from "lucide-react";
import { Sidebar } from "./components/sidebar";

interface EmployerDashboardLayoutProps {
	children: ReactNode;
}

export const employerDashboardNavItems = [
	{
		title: "לוח בקרה",
		href: "/employer-dashboard",
		icon: <Home className="h-4 w-4" />,
	},
	{
		title: "עובדים",
		href: "/employer-dashboard/employees",
		icon: <Users className="h-4 w-4" />,
	},
	{
		title: "תלושי שכר",
		href: "/employer-dashboard/payslips",
		icon: <Receipt className="h-4 w-4" />,
	},
	{
		title: "טפסים",
		href: "/employer-dashboard/forms",
		icon: <FileText className="h-4 w-4" />,
	},
	{
		title: "מעסיקים",
		href: "/employer-dashboard/employers",
		icon: <Building className="h-4 w-4" />,
	},
	{
		title: "הגדרות",
		href: "/employer-dashboard/settings",
		icon: <Settings className="h-4 w-4" />,
	},
];

export default function EmployerDashboardLayout({
	children,
}: EmployerDashboardLayoutProps) {
	const { data: session, status } = useSession();
	
	if (status === "loading") {
		return <div>טוען...</div>;
	}
	
	if (!session) {
		redirect("/login");
	}
	
	return (
		<div className="flex min-h-screen flex-col">
			<div className="container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
				<aside className="fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block">
					<Sidebar />
				</aside>
				<main className="flex w-full flex-col overflow-hidden">
					{children}
				</main>
			</div>
		</div>
	);
}

