import { type Form101 } from "@prisma/client";
import { PDFDocument } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';

export async function generateForm101PDF(
  form101: Form101,
  employeeData: {
    firstName: string;
    lastName: string;
    nationalId: string;
    email?: string | null;
    phone?: string | null;
  }
): Promise<Buffer> {
  try {
    // Load the template PDF
    const templatePath = path.join(process.cwd(), 'public', '101.pdf');
    const templateBytes = await fs.readFile(templatePath);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(templateBytes);
    const form = pdfDoc.getForm();

    // Fill in the form fields based on Form101 and employee data
    // Note: These field names should match the actual field names in the PDF
    // You may need to inspect the PDF to get the exact field names

    try {
      // Employee details
      const employeeName = `${employeeData.firstName} ${employeeData.lastName}`;

      // Try to fill common field names - catch errors for fields that don't exist
      const fieldMappings = [
        { fieldName: 'employeeName', value: employeeName },
        { fieldName: 'employee_name', value: employeeName },
        { fieldName: 'name', value: employeeName },
        { fieldName: 'nationalId', value: employeeData.nationalId },
        { fieldName: 'national_id', value: employeeData.nationalId },
        { fieldName: 'id_number', value: employeeData.nationalId },
        { fieldName: 'taxYear', value: form101.taxYear?.toString() || new Date().getFullYear().toString() },
        { fieldName: 'tax_year', value: form101.taxYear?.toString() || new Date().getFullYear().toString() },
        { fieldName: 'year', value: form101.taxYear?.toString() || new Date().getFullYear().toString() },
        { fieldName: 'childrenCount', value: form101.childrenCount.toString() },
        { fieldName: 'children_count', value: form101.childrenCount.toString() },
        { fieldName: 'children', value: form101.childrenCount.toString() },
        { fieldName: 'childrenUnder18', value: form101.childrenUnder18?.toString() || '0' },
        { fieldName: 'children_under_18', value: form101.childrenUnder18?.toString() || '0' },
        { fieldName: 'childrenUnder5', value: form101.childrenUnder5?.toString() || '0' },
        { fieldName: 'children_under_5', value: form101.childrenUnder5?.toString() || '0' },
      ];

      // Try to fill text fields
      fieldMappings.forEach(({ fieldName, value }) => {
        try {
          const field = form.getTextField(fieldName);
          field.setText(value);
        } catch (error) {
          // Field doesn't exist, continue
        }
      });

      // Try to handle marital status
      if (form101.maritalStatus) {
        const maritalStatusMappings = [
          'maritalStatus',
          'marital_status',
          'status'
        ];

        maritalStatusMappings.forEach(fieldName => {
          try {
            const radioGroup = form.getRadioGroup(fieldName);
            radioGroup.select(form101.maritalStatus!.toLowerCase());
          } catch (error) {
            // Try as checkbox instead
            try {
              const checkbox = form.getCheckBox(`${fieldName}_${form101.maritalStatus!.toLowerCase()}`);
              checkbox.check();
            } catch (error) {
              // Field doesn't exist, continue
            }
          }
        });
      }

      // Try to handle boolean fields
      const booleanMappings = [
        { fieldNames: ['spouseWorks', 'spouse_works', 'spouse'], value: form101.spouseWorks },
        { fieldNames: ['isMainEmployer', 'is_main_employer', 'main_employer'], value: form101.isMainEmployer },
        { fieldNames: ['hasAdditionalIncome', 'has_additional_income', 'additional_income'], value: form101.hasAdditionalIncome },
      ];

      booleanMappings.forEach(({ fieldNames, value }) => {
        fieldNames.forEach(fieldName => {
          try {
            const checkbox = form.getCheckBox(fieldName);
            if (value) {
              checkbox.check();
            } else {
              checkbox.uncheck();
            }
          } catch (error) {
            // Field doesn't exist, continue
          }
        });
      });

      // Try to fill additional fields if they exist
      if (form101.additionalCreditPoints) {
        const creditPointsFields = ['additionalCreditPoints', 'additional_credit_points', 'credit_points'];
        creditPointsFields.forEach(fieldName => {
          try {
            const field = form.getTextField(fieldName);
            field.setText(form101.additionalCreditPoints!.toString());
          } catch (error) {
            // Field doesn't exist, continue
          }
        });
      }

      if (form101.taxCoordinationNumber) {
        const taxCoordFields = ['taxCoordinationNumber', 'tax_coordination_number', 'coordination_number'];
        taxCoordFields.forEach(fieldName => {
          try {
            const field = form.getTextField(fieldName);
            field.setText(form101.taxCoordinationNumber!);
          } catch (error) {
            // Field doesn't exist, continue
          }
        });
      }

    } catch (error) {
      console.warn('Some form fields could not be filled:', error);
      // Continue with PDF generation even if some fields fail
    }

    // Save the filled PDF
    const pdfBytes = await pdfDoc.save();
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error('Error generating Form 101 PDF:', error);
    throw new Error('Failed to generate Form 101 PDF');
  }
}