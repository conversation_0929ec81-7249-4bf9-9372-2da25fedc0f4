import { type Form101 } from "@prisma/client";
import { PDFDocument } from 'pdf-lib';
import fs from 'fs/promises';
import path from 'path';

export async function generateForm101PDF(
  form101: Form101,
  employeeData: {
    firstName: string;
    lastName: string;
    nationalId: string;
    email?: string | null;
    phone?: string | null;
  },
  employerData?: {
    name: string;
    businessNumber: string;
    address?: string;
    phone?: string;
  }
): Promise<Buffer> {
  try {
    // Load the template PDF
    const templatePath = path.join(process.cwd(), 'public', '101.pdf');
    const templateBytes = await fs.readFile(templatePath);

    // Load the PDF document
    const pdfDoc = await PDFDocument.load(templateBytes);
    const form = pdfDoc.getForm();

    // Debug: Log available fields in the PDF
    const fields = form.getFields();
    console.log('Available PDF fields:', fields.map(field => ({
      name: field.getName(),
      type: field.constructor.name
    })));

    // Fill in the form fields based on Form101 and employee data
    // Note: These field names should match the actual field names in the PDF
    // You may need to inspect the PDF to get the exact field names

    try {
      // Employee details
      const employeeName = `${employeeData.firstName} ${employeeData.lastName}`;
      const currentDate = new Date();
      const taxYear = form101.taxYear || currentDate.getFullYear();

      // Comprehensive field mappings for Form 101
      const fieldMappings = [
        // Employee Information
        { fieldName: 'employeeName', value: employeeName },
        { fieldName: 'employee_name', value: employeeName },
        { fieldName: 'name', value: employeeName },
        { fieldName: 'fullName', value: employeeName },
        { fieldName: 'שם_מלא', value: employeeName },
        { fieldName: 'שם_העובד', value: employeeName },

        { fieldName: 'firstName', value: employeeData.firstName },
        { fieldName: 'שם_פרטי', value: employeeData.firstName },

        { fieldName: 'lastName', value: employeeData.lastName },
        { fieldName: 'שם_משפחה', value: employeeData.lastName },

        { fieldName: 'nationalId', value: employeeData.nationalId },
        { fieldName: 'national_id', value: employeeData.nationalId },
        { fieldName: 'id_number', value: employeeData.nationalId },
        { fieldName: 'תעודת_זהות', value: employeeData.nationalId },
        { fieldName: 'ת_ז', value: employeeData.nationalId },

        // Tax Year
        { fieldName: 'taxYear', value: taxYear.toString() },
        { fieldName: 'tax_year', value: taxYear.toString() },
        { fieldName: 'year', value: taxYear.toString() },
        { fieldName: 'שנת_מס', value: taxYear.toString() },

        // Date fields
        { fieldName: 'date', value: currentDate.toLocaleDateString('he-IL') },
        { fieldName: 'תאריך', value: currentDate.toLocaleDateString('he-IL') },
        { fieldName: 'currentDate', value: currentDate.toLocaleDateString('he-IL') },

        // Children Information
        { fieldName: 'childrenCount', value: form101.childrenCount.toString() },
        { fieldName: 'children_count', value: form101.childrenCount.toString() },
        { fieldName: 'children', value: form101.childrenCount.toString() },
        { fieldName: 'מספר_ילדים', value: form101.childrenCount.toString() },
        { fieldName: 'ילדים', value: form101.childrenCount.toString() },

        { fieldName: 'childrenUnder18', value: form101.childrenUnder18?.toString() || '0' },
        { fieldName: 'children_under_18', value: form101.childrenUnder18?.toString() || '0' },
        { fieldName: 'ילדים_מתחת_18', value: form101.childrenUnder18?.toString() || '0' },

        { fieldName: 'childrenUnder5', value: form101.childrenUnder5?.toString() || '0' },
        { fieldName: 'children_under_5', value: form101.childrenUnder5?.toString() || '0' },
        { fieldName: 'ילדים_מתחת_5', value: form101.childrenUnder5?.toString() || '0' },

        // Contact Information
        { fieldName: 'phone', value: employeeData.phone || '' },
        { fieldName: 'telephone', value: employeeData.phone || '' },
        { fieldName: 'טלפון', value: employeeData.phone || '' },

        { fieldName: 'email', value: employeeData.email || '' },
        { fieldName: 'דואל', value: employeeData.email || '' },

        // Marital Status
        { fieldName: 'maritalStatus', value: form101.maritalStatus || '' },
        { fieldName: 'marital_status', value: form101.maritalStatus || '' },
        { fieldName: 'מצב_משפחתי', value: form101.maritalStatus === 'SINGLE' ? 'רווק' :
                                                form101.maritalStatus === 'MARRIED' ? 'נשוי' :
                                                form101.maritalStatus === 'DIVORCED' ? 'גרוש' :
                                                form101.maritalStatus === 'WIDOWED' ? 'אלמן' : '' },
      ];

      // Add employer information if provided
      if (employerData) {
        fieldMappings.push(
          { fieldName: 'employerName', value: employerData.name },
          { fieldName: 'employer_name', value: employerData.name },
          { fieldName: 'שם_מעסיק', value: employerData.name },
          { fieldName: 'חברה', value: employerData.name },

          { fieldName: 'businessNumber', value: employerData.businessNumber },
          { fieldName: 'business_number', value: employerData.businessNumber },
          { fieldName: 'מספר_עסק', value: employerData.businessNumber },
          { fieldName: 'ח_פ', value: employerData.businessNumber },

          { fieldName: 'employerAddress', value: employerData.address || '' },
          { fieldName: 'employer_address', value: employerData.address || '' },
          { fieldName: 'כתובת_מעסיק', value: employerData.address || '' },

          { fieldName: 'employerPhone', value: employerData.phone || '' },
          { fieldName: 'employer_phone', value: employerData.phone || '' },
          { fieldName: 'טלפון_מעסיק', value: employerData.phone || '' }
        );
      }

      // Additional credit points
      if (form101.additionalCreditPoints) {
        fieldMappings.push(
          { fieldName: 'additionalCreditPoints', value: form101.additionalCreditPoints.toString() },
          { fieldName: 'additional_credit_points', value: form101.additionalCreditPoints.toString() },
          { fieldName: 'נקודות_זיכוי_נוספות', value: form101.additionalCreditPoints.toString() }
        );
      }

      // Tax coordination number
      if (form101.taxCoordinationNumber) {
        fieldMappings.push(
          { fieldName: 'taxCoordinationNumber', value: form101.taxCoordinationNumber },
          { fieldName: 'tax_coordination_number', value: form101.taxCoordinationNumber },
          { fieldName: 'מספר_תיאום_מס', value: form101.taxCoordinationNumber }
        );
      }

      // Try to fill text fields
      let filledFieldsCount = 0;
      fieldMappings.forEach(({ fieldName, value }) => {
        try {
          const field = form.getTextField(fieldName);
          field.setText(value);
          filledFieldsCount++;
          console.log(`✅ Filled field: ${fieldName} = ${value}`);
        } catch (error) {
          // Field doesn't exist, continue
          console.log(`❌ Field not found: ${fieldName}`);
        }
      });

      console.log(`📊 Successfully filled ${filledFieldsCount} out of ${fieldMappings.length} text fields`);

      // Handle marital status - try multiple approaches
      if (form101.maritalStatus) {
        const maritalStatusMappings = [
          'maritalStatus',
          'marital_status',
          'status',
          'מצב_משפחתי',
          'מצב_אישי'
        ];

        const statusValue = form101.maritalStatus.toLowerCase();
        const hebrewStatus = form101.maritalStatus === 'SINGLE' ? 'רווק' :
                           form101.maritalStatus === 'MARRIED' ? 'נשוי' :
                           form101.maritalStatus === 'DIVORCED' ? 'גרוש' :
                           form101.maritalStatus === 'WIDOWED' ? 'אלמן' : statusValue;

        maritalStatusMappings.forEach(fieldName => {
          // Try as radio group
          try {
            const radioGroup = form.getRadioGroup(fieldName);
            // Try different value formats
            const valuesToTry = [statusValue, form101.maritalStatus!, hebrewStatus];
            for (const value of valuesToTry) {
              try {
                radioGroup.select(value);
                break;
              } catch (e) {
                // Continue to next value
              }
            }
          } catch (error) {
            // Try as individual checkboxes
            const checkboxesToTry = [
              `${fieldName}_${statusValue}`,
              `${fieldName}_${form101.maritalStatus}`,
              `${fieldName}_${hebrewStatus}`,
              `${statusValue}`,
              `${form101.maritalStatus}`,
              `${hebrewStatus}`,
              `רווק`, `נשוי`, `גרוש`, `אלמן` // Direct Hebrew options
            ];

            checkboxesToTry.forEach(checkboxName => {
              try {
                const checkbox = form.getCheckBox(checkboxName);
                if (checkboxName.includes(statusValue) ||
                    checkboxName.includes(form101.maritalStatus!) ||
                    checkboxName === hebrewStatus) {
                  checkbox.check();
                }
              } catch (error) {
                // Field doesn't exist, continue
              }
            });
          }
        });
      }

      // Handle boolean fields with comprehensive mapping
      const booleanMappings = [
        {
          fieldNames: [
            'spouseWorks', 'spouse_works', 'spouse', 'בן_זוג_עובד', 'בת_זוג_עובדת',
            'spouse_working', 'spouse_employed'
          ],
          value: form101.spouseWorks,
          yesOptions: ['כן', 'yes', 'true', '1'],
          noOptions: ['לא', 'no', 'false', '0']
        },
        {
          fieldNames: [
            'isMainEmployer', 'is_main_employer', 'main_employer', 'מעסיק_עיקרי',
            'primary_employer', 'main_job'
          ],
          value: form101.isMainEmployer,
          yesOptions: ['כן', 'yes', 'true', '1'],
          noOptions: ['לא', 'no', 'false', '0']
        },
        {
          fieldNames: [
            'hasAdditionalIncome', 'has_additional_income', 'additional_income', 'הכנסה_נוספת',
            'other_income', 'extra_income'
          ],
          value: form101.hasAdditionalIncome,
          yesOptions: ['כן', 'yes', 'true', '1'],
          noOptions: ['לא', 'no', 'false', '0']
        },
      ];

      booleanMappings.forEach(({ fieldNames, value, yesOptions, noOptions }) => {
        fieldNames.forEach(fieldName => {
          // Try as checkbox
          try {
            const checkbox = form.getCheckBox(fieldName);
            if (value) {
              checkbox.check();
            } else {
              checkbox.uncheck();
            }
          } catch (error) {
            // Try as radio group with yes/no options
            try {
              const radioGroup = form.getRadioGroup(fieldName);
              const optionsToTry = value ? yesOptions : noOptions;
              for (const option of optionsToTry) {
                try {
                  radioGroup.select(option);
                  break;
                } catch (e) {
                  // Continue to next option
                }
              }
            } catch (error) {
              // Try as separate yes/no checkboxes
              const optionsToTry = value ? yesOptions : noOptions;
              optionsToTry.forEach(option => {
                try {
                  const checkbox = form.getCheckBox(`${fieldName}_${option}`);
                  checkbox.check();
                } catch (error) {
                  // Field doesn't exist, continue
                }
              });
            }
          }
        });
      });

      // Try to fill additional fields if they exist
      if (form101.additionalCreditPoints) {
        const creditPointsFields = ['additionalCreditPoints', 'additional_credit_points', 'credit_points'];
        creditPointsFields.forEach(fieldName => {
          try {
            const field = form.getTextField(fieldName);
            field.setText(form101.additionalCreditPoints!.toString());
          } catch (error) {
            // Field doesn't exist, continue
          }
        });
      }

      if (form101.taxCoordinationNumber) {
        const taxCoordFields = ['taxCoordinationNumber', 'tax_coordination_number', 'coordination_number'];
        taxCoordFields.forEach(fieldName => {
          try {
            const field = form.getTextField(fieldName);
            field.setText(form101.taxCoordinationNumber!);
          } catch (error) {
            // Field doesn't exist, continue
          }
        });
      }

    } catch (error) {
      console.warn('Some form fields could not be filled:', error);
      // Continue with PDF generation even if some fields fail
    }

    // Save the filled PDF
    const pdfBytes = await pdfDoc.save();

    console.log(`🎉 Form 101 PDF generated successfully for ${employeeData.firstName} ${employeeData.lastName}`);
    if (employerData) {
      console.log(`📋 Employer: ${employerData.name} (${employerData.businessNumber})`);
    }

    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error('Error generating Form 101 PDF:', error);
    throw new Error('Failed to generate Form 101 PDF');
  }
}