import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Prisma } from "@prisma/client";
import { getSignedUploadUrl, getPublicUrl, deleteFile, getSignedDownloadUrl } from "@/server/s3";
import { randomUUID } from "crypto";
import path from "path";
import { getSignatureService } from "@/lib/signature-service";
import { generateForm101PDF } from "@/lib/form101-pdf";
import { safeDocumentOperation } from "./document-utils";

// Define custom interface for Document with additional fields used in the code
interface DocumentWithCustomFields {
  id: string;
  title: string | null;
  fileName: string;
  fileType: string;
  fileSize: number;
  url: string;
  s3Key: string;
  category: string | null;
  uploadedAt: Date;
  metadata: any;
  description?: string | null;
  employeeId?: string | null;
  tenantId: string;
  employerId?: string | null;
  referenceModel?: string;
  referenceId?: string;
}

// Helper function to safely cast Document to our custom type
function asDocumentWithCustomFields(doc: any): DocumentWithCustomFields {
  return doc as DocumentWithCustomFields;
}

// Decorate the document functions to handle type casting
const safeDocument = {
  findFirst: async (args: any, db: any) => {
    const doc = await db.document.findFirst(args);
    return doc ? asDocumentWithCustomFields(doc) : null;
  },
  findMany: async (args: any, db: any) => {
    const docs = await db.document.findMany(args);
    return docs.map((doc: any) => asDocumentWithCustomFields(doc));
  },
  update: async (args: any, db: any) => {
    const doc = await db.document.update(args);
    return asDocumentWithCustomFields(doc);
  },
  create: async (args: any, db: any) => {
    const doc = await db.document.create(args);
    return asDocumentWithCustomFields(doc);
  }
};

// Helper function to generate a unique file path in S3 for employee documents
function generateEmployeeS3Key(
  employeeId: string,
  fileName: string,
  category: string = "general"
) {
  // ensure filename is safe and bounded
  const safeName = path.basename(fileName).slice(0, 100);
  const uniqueFileName = `${randomUUID()}-${safeName}`;
  return `employee-docs/${employeeId}/${category}/${uniqueFileName}`;
}

// Document categories for employees
const EMPLOYEE_DOCUMENT_CATEGORIES = {
  ID_CARD: "id-card",           // תעודת זהות
  PASSPORT: "passport",         // דרכון
  FORM_101: "form-101",         // טופס 101
  CONTRACT: "contract",         // חוזה עבודה
  BANK_DETAILS: "bank-details", // פרטי בנק
  MEDICAL: "medical",           // מסמכים רפואיים
  EDUCATION: "education",       // תעודות השכלה
  VISA: "visa",                 // ויזה
  OTHER: "other"                // מסמכים אחרים
} as const;

export const employeeRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({
      employerId: z.string(),
      fullName: z.string().min(2),
      nationalId: z.string().min(9).max(9),
      email: z.string().email().optional().or(z.literal("")),
      phone: z.string().optional().or(z.literal("")),
      address: z.string().optional(),
      startDate: z.string(),
      baseSalary: z.string(),
      position: z.string().min(2),
      department: z.string().optional(),
      bankAccount: z.string().optional(),
      bankBranch: z.string().optional(),
      bankName: z.string().optional(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Verify employer exists and belongs to tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });

        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }

        // Split full name into first and last name
        const nameParts = input.fullName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        // Parse salary
        const baseSalary = parseFloat(input.baseSalary);
        if (isNaN(baseSalary)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid salary amount",
          });
        }

        // Create contact object
        const contact: any = {};
        if (input.email) contact.email = input.email;
        if (input.phone) contact.phone = input.phone;

        // Create bank details object
        const bankDetails: any = {};
        if (input.bankName) bankDetails.bankName = input.bankName;
        if (input.bankBranch) bankDetails.bankBranch = input.bankBranch;
        if (input.bankAccount) bankDetails.bankAccount = input.bankAccount;

        // Create the employee
        const employee = await db.employee.create({
          data: {
            tenantId: user.tenantId,
            employerId: input.employerId,
            firstName,
            lastName,
            nationalId: input.nationalId,
            startDate: new Date(input.startDate),
            baseSalary,
            status: "ACTIVE",
            contact: Object.keys(contact).length > 0 ? contact : undefined,
            address: input.address || undefined,
          }
        });

        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "CREATE",
            modelName: "Employee",
            recordId: employee.id,
            newValues: {
              fullName: input.fullName,
              nationalId: input.nationalId,
              position: input.position,
              baseSalary: baseSalary
            }
          }
        });

        return {
          success: true,
          employee: {
            ...employee,
            fullName: `${firstName} ${lastName}`,
          }
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error creating employee"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create employee",
          cause: error,
        });
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const employeeId = input.id;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Get employee with related data
        const employee = await db.employee.findFirst({
          where: {
            id: employeeId,
            tenantId: user.tenantId
          },
          include: {
            department: {
              select: {
                id: true,
                name: true
              }
            },
            payslips: {
              orderBy: [
                { year: 'desc' },
                { month: 'desc' }
              ],
              take: 1,
              select: {
                year: true,
                month: true,
                grossPay: true,
                netPay: true
              }
            }
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Extract contact info from JSON
        const contact = employee.contact as any || {};
        const email = contact.email || null;
        const phone = contact.phone || null;

        return {
          ...employee,
          fullName: `${employee.firstName} ${employee.lastName}`,
          email,
          phone,
          latestPayslip: employee.payslips[0] ? {
            period: `${employee.payslips[0].month}/${employee.payslips[0].year}`,
            grossSalary: employee.payslips[0].grossPay,
            netSalary: employee.payslips[0].netPay
          } : null
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.id },
          "Error fetching employee"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employee",
          cause: error,
        });
      }
    }),

  update: protectedProcedure
    .input(z.object({
      id: z.string(),
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      status: z.enum(["ACTIVE", "TERMINATED", "SUSPENDED"]).optional(),
      birthDate: z.string().optional(),
      email: z.string().email().optional().nullable(),
      phone: z.string().optional().nullable(),
      startDate: z.string().optional(),
      endDate: z.string().optional().nullable(),
      baseSalary: z.number().optional(),
      travelAllowance: z.number().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const { id: employeeId, email, phone, ...updateData } = input;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to the user's tenant
        const existingEmployee = await db.employee.findFirst({
          where: {
            id: employeeId,
            tenantId: user.tenantId
          }
        });

        if (!existingEmployee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Prepare update data
        const prismaUpdateData: Prisma.EmployeeUpdateInput = {};

        if (updateData.firstName !== undefined) prismaUpdateData.firstName = updateData.firstName;
        if (updateData.lastName !== undefined) prismaUpdateData.lastName = updateData.lastName;
        if (updateData.status !== undefined) prismaUpdateData.status = updateData.status;
        if (updateData.birthDate !== undefined) prismaUpdateData.birthDate = new Date(updateData.birthDate);
        if (updateData.startDate !== undefined) prismaUpdateData.startDate = new Date(updateData.startDate);
        if (updateData.endDate !== undefined) prismaUpdateData.endDate = updateData.endDate ? new Date(updateData.endDate) : null;
        if (updateData.baseSalary !== undefined) prismaUpdateData.baseSalary = updateData.baseSalary;
        if (updateData.travelAllowance !== undefined) prismaUpdateData.travelAllowance = updateData.travelAllowance;

        // Update contact info if provided
        if (email !== undefined || phone !== undefined) {
          const currentContact = (existingEmployee.contact as any) || {};
          prismaUpdateData.contact = {
            ...currentContact,
            ...(email !== undefined && { email }),
            ...(phone !== undefined && { phone })
          };
        }

        // Update the employee
        const updatedEmployee = await db.employee.update({
          where: { id: employeeId },
          data: prismaUpdateData,
          include: {
            department: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });

        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "UPDATE",
            modelName: "Employee",
            recordId: employeeId,
            oldValues: {
              firstName: existingEmployee.firstName,
              lastName: existingEmployee.lastName,
              status: existingEmployee.status,
              baseSalary: existingEmployee.baseSalary
            },
            newValues: updateData
          }
        });

        return {
          success: true,
          employee: updatedEmployee
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.id },
          "Error updating employee"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update employee",
          cause: error,
        });
      }
    }),

  getPayslips: protectedProcedure
    .input(z.object({
      employeeId: z.string(),
      page: z.number().int().positive().default(1),
      limit: z.number().int().positive().default(12),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const { employeeId, page, limit } = input;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Verify employee belongs to tenant
        const employee = await db.employee.findFirst({
          where: {
            id: employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Calculate pagination
        const skip = (page - 1) * limit;

        // Get total count
        const totalCount = await db.payslip.count({
          where: { employeeId }
        });

        // Get payslips
        const payslips = await db.payslip.findMany({
          where: { employeeId },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ],
          skip,
          take: limit,
          include: {
            items: {
              orderBy: [
                { type: 'asc' },
                { description: 'asc' }
              ]
            }
          }
        });

        return {
          payslips: payslips.map(payslip => ({
            ...payslip,
            period: `${payslip.month}/${payslip.year}`,
            itemsCount: payslip.items.length
          })),
          totalCount,
          pageCount: Math.ceil(totalCount / limit)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error fetching payslips"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payslips",
          cause: error,
        });
      }
    }),

  getSalaryTransactions: protectedProcedure
    .input(z.object({
      employeeId: z.string(),
      year: z.number().optional(),
      month: z.number().optional(),
      page: z.number().int().positive().default(1),
      limit: z.number().int().positive().default(20),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const { employeeId, year, month, page, limit } = input;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Build where clause
        const where: Prisma.SalaryTransactionWhereInput = {
          employeeId,
          tenantId: user.tenantId,
          ...(year && { periodYear: year }),
          ...(month && { periodMonth: month })
        };

        // Calculate pagination
        const skip = (page - 1) * limit;

        // Get total count
        const totalCount = await db.salaryTransaction.count({ where });

        // Get salary transactions
        const transactions = await db.salaryTransaction.findMany({
          where,
          orderBy: [
            { periodYear: 'desc' },
            { periodMonth: 'desc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit,
          include: {
            department: {
              select: {
                id: true,
                name: true
              }
            }
          }
        });

        return {
          transactions: transactions.map(transaction => ({
            ...transaction,
            period: `${transaction.periodMonth}/${transaction.periodYear}`
          })),
          totalCount,
          pageCount: Math.ceil(totalCount / limit)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error fetching salary transactions"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch salary transactions",
          cause: error,
        });
      }
    }),

  getPayslipDetails: protectedProcedure
    .input(z.object({
      payslipId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const { payslipId } = input;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Get payslip with full details
        const payslip = await db.payslip.findFirst({
          where: {
            id: payslipId,
            tenantId: user.tenantId
          },
          include: {
            employee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                nationalId: true
              }
            },
            items: {
              orderBy: [
                { type: 'asc' },
                { description: 'asc' }
              ]
            }
          }
        });

        if (!payslip) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Payslip not found",
          });
        }

        return {
          ...payslip,
          period: `${payslip.month}/${payslip.year}`,
          employeeName: `${payslip.employee.firstName} ${payslip.employee.lastName}`
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, payslipId: input.payslipId },
          "Error fetching payslip details"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payslip details",
          cause: error,
        });
      }
    }),

  // Get employee documents
  getDocuments: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      category: z.enum(Object.values(EMPLOYEE_DOCUMENT_CATEGORIES) as [string, ...string[]]).optional(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(20)
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        const skip = (input.page - 1) * input.limit;

        // Build where clause
        const whereClause = {
          employeeId: input.employeeId,
          tenantId: user.tenantId,
          ...(input.category && { category: input.category })
        };

        // Get documents with pagination
        const [documents, totalCount] = await Promise.all([
          db.document.findMany({
            where: whereClause,
            orderBy: { uploadedAt: 'desc' },
            skip,
            take: input.limit,
            select: {
              id: true,
              title: true,
              fileName: true,
              fileType: true,
              fileSize: true,
              url: true,
              s3Key: true,
              category: true,
              uploadedAt: true,
              metadata: true
            }
          }),
          db.document.count({ where: whereClause })
        ]);

        const pageCount = Math.ceil(totalCount / input.limit);

        return {
          documents,
          totalCount,
          pageCount,
          currentPage: input.page
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error fetching employee documents"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch documents",
          cause: error,
        });
      }
    }),

  // Get signed upload URL for employee documents
  getDocumentUploadUrl: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      fileName: z.string().min(1),
      fileType: z.string().min(1),
      category: z.enum(Object.values(EMPLOYEE_DOCUMENT_CATEGORIES) as [string, ...string[]]).default("general")
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Sanitize and truncate fileName
        let safeFileName = path.basename(input.fileName);
        if (safeFileName.length > 100) {
          const ext = path.extname(safeFileName);
          safeFileName = safeFileName.slice(0, 100 - ext.length) + ext;
        }

        // Generate a unique key for S3
        const fileKey = generateEmployeeS3Key(input.employeeId, safeFileName, input.category);

        // Get a signed upload URL
        const signedUrl = await getSignedUploadUrl(fileKey, input.fileType, 3600);

        return {
          signedUrl,
          fileKey,
          publicUrl: getPublicUrl(fileKey)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error generating signed upload URL"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate upload URL",
          cause: error,
        });
      }
    }),

  // Register document after upload
  registerDocument: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      title: z.string().optional(),
      fileName: z.string().min(1),
      fileType: z.string().min(1),
      fileSize: z.number().min(1),
      fileKey: z.string().min(1),
      category: z.enum(Object.values(EMPLOYEE_DOCUMENT_CATEGORIES) as [string, ...string[]]).default("general"),
      year: z.number().optional(), // For Form 101 and other yearly documents
      description: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Validate fileKey prefix
        if (!input.fileKey.startsWith(`employee-docs/${input.employeeId}/`)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Invalid fileKey"
          });
        }

        // Get the public URL for the file
        const publicUrl = getPublicUrl(input.fileKey);

        // Create metadata object
        const metadata = {
          uploadedBy: userId,
          ...(input.year && { year: input.year }),
          ...(input.description && { description: input.description }),
          category: input.category
        };

        // Create a document record in the database
        const document = await db.document.create({
          data: {
            tenantId: user.tenantId,
            title: input.title || input.fileName,
            mimeType: input.fileType,
            uploadedBy: userId,
            fileName: input.fileName,
            fileType: input.fileType,
            fileSize: input.fileSize,
            url: publicUrl,
            s3Key: input.fileKey,
            category: input.category,
            referenceModel: "Employee",
            referenceId: input.employeeId,
            employeeId: input.employeeId,
            metadata
          }
        });

        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "CREATE",
            modelName: "Document",
            recordId: document.id,
            newValues: {
              title: document.title,
              fileName: document.fileName,
              employeeId: document.employeeId,
              category: document.category,
              s3Key: document.s3Key
            }
          }
        });

        return {
          success: true,
          document: {
            id: document.id,
            title: document.title,
            fileName: document.fileName,
            fileType: document.fileType,
            fileSize: document.fileSize,
            url: document.url,
            s3Key: document.s3Key,
            category: document.category,
            uploadedAt: document.uploadedAt,
            metadata: document.metadata
          }
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error registering employee document"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to register document",
          cause: error,
        });
      }
    }),

  // Delete employee document
  deleteDocument: protectedProcedure
    .input(z.object({
      documentId: z.string().min(1),
      employeeId: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if document exists and belongs to the user's tenant and employee
        const document = await db.document.findFirst({
          where: {
            id: input.documentId,
            employeeId: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!document) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Document not found",
          });
        }

        // Delete from S3 if there's a key
        if (document.s3Key) {
          try {
            await deleteFile(document.s3Key);
          } catch (s3Error) {
            ctx.logger?.error(
              { err: s3Error, userId, documentId: input.documentId },
              "Error deleting file from S3"
            );
          }
        }

        // Delete from database
        await db.document.delete({
          where: { id: input.documentId }
        });

        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "DELETE",
            modelName: "Document",
            recordId: document.id,
            oldValues: {
              title: document.title,
              fileName: document.fileName,
              employeeId: document.employeeId,
              category: document.category,
              s3Key: document.s3Key
            }
          }
        });

        return { success: true };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, documentId: input.documentId },
          "Error deleting employee document"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete document",
          cause: error,
        });
      }
    }),

  // Get signed download URL for employee document
  getDocumentDownloadUrl: protectedProcedure
    .input(z.object({
      documentId: z.string().min(1),
      employeeId: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if document exists and belongs to the user's tenant and employee
        const document = await db.document.findFirst({
          where: {
            id: input.documentId,
            employeeId: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!document || !document.s3Key) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Document not found",
          });
        }

        // Generate signed download URL
        const signedUrl = await getSignedDownloadUrl(document.s3Key, 3600); // 1 hour expiry

        return {
          signedUrl,
          fileName: document.fileName,
          fileType: document.fileType
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, documentId: input.documentId },
          "Error generating signed download URL"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate download URL",
          cause: error,
        });
      }
    }),

  // Get Form 101 documents
  getForm101Documents: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      year: z.number().optional()
    }))
    .query(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Wrap the whole operation in our safe document handler
      return await safeDocumentOperation(async () => {
        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Build where clause for Form 101 documents
        const whereClause = {
          employeeId: input.employeeId,
          tenantId: user.tenantId,
          category: EMPLOYEE_DOCUMENT_CATEGORIES.FORM_101,
          ...(input.year && {
            metadata: {
              path: ['year'],
              equals: input.year
            }
          })
        };

        // Get Form 101 documents
        const documents = await db.document.findMany({
          where: whereClause,
          orderBy: [
            { uploadedAt: 'desc' }
          ],
          select: {
            id: true,
            title: true,
            fileName: true,
            fileType: true,
            fileSize: true,
            url: true,
            s3Key: true,
            uploadedAt: true,
            metadata: true
          }
        });

        return {
          success: true,
          documents,
          employeeId: input.employeeId,
          year: input.year
        };
      }, {
        userId,
        employeeId: input.employeeId,
        operation: "fetching Form 101 documents",
        logger: ctx.logger
      });
    }),

  // Get ID card documents
  getIdCardDocuments: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1)
    }))
    .query(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Wrap the whole operation in our safe document handler
      return await safeDocumentOperation(async () => {
        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          },
          select: {
            id: true,
            nationalId: true,
            firstName: true,
            lastName: true
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Get ID card documents
        const documents = await db.document.findMany({
          where: {
            employeeId: input.employeeId,
            tenantId: user.tenantId,
            category: EMPLOYEE_DOCUMENT_CATEGORIES.ID_CARD
          },
          orderBy: { uploadedAt: 'desc' },
          select: {
            id: true,
            title: true,
            fileName: true,
            fileType: true,
            fileSize: true,
            url: true,
            s3Key: true,
            uploadedAt: true,
            metadata: true
          }
        });

        return {
          success: true,
          employee: {
            id: employee.id,
            nationalId: employee.nationalId,
            fullName: `${employee.firstName} ${employee.lastName}`
          },
          documents
        };
      }, {
        userId,
        employeeId: input.employeeId,
        operation: "fetching ID card documents",
        logger: ctx.logger
      });
    }),

  // Update document metadata
  updateDocumentMetadata: protectedProcedure
    .input(z.object({
      documentId: z.string().min(1),
      employeeId: z.string().min(1),
      title: z.string().optional(),
      description: z.string().optional(),
      year: z.number().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if document exists and belongs to the user's tenant and employee
        const existingDocument = await db.document.findFirst({
          where: {
            id: input.documentId,
            employeeId: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!existingDocument) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Document not found",
          });
        }

        // Update metadata
        const currentMetadata = (existingDocument.metadata as any) || {};
        const updatedMetadata = {
          ...currentMetadata,
          ...(input.description !== undefined && { description: input.description }),
          ...(input.year !== undefined && { year: input.year }),
          lastModifiedBy: userId,
          lastModifiedAt: new Date().toISOString()
        };

        // Update document
        const updatedDocument = await db.document.update({
          where: { id: input.documentId },
          data: {
            ...(input.title !== undefined && { title: input.title }),
            metadata: updatedMetadata
          }
        });

        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "UPDATE",
            modelName: "Document",
            recordId: updatedDocument.id,
                                      oldValues: {
              title: existingDocument.title,
              metadata: (existingDocument as any).metadata
            },
            newValues: {
              title: updatedDocument.title,
              metadata: (updatedDocument as any).metadata
            }
          }
        });

        return {
          success: true,
          document: {
            id: updatedDocument.id,
            title: updatedDocument.title,
            metadata: (updatedDocument as any).metadata
          }
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, documentId: input.documentId },
          "Error updating document metadata"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update document",
          cause: error,
        });
      }
    }),

  // Get Form 101 data
  getForm101: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      year: z.number().optional()
    }))
    .query(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Get user's tenant
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true }
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get Form 101 data
      const form101 = await db.form101.findFirst({
        where: {
          tenantId: user.tenantId,
          employeeId: input.employeeId,
          taxYear: input.year || new Date().getFullYear()
        }
      });

      return form101;
    }),

  // Get all Form 101 data for a year
  getAllForm101: protectedProcedure
    .input(z.object({
      year: z.number().default(() => new Date().getFullYear())
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Get all Form 101 data for the year
        const forms101 = await db.form101.findMany({
          where: {
            tenantId: user.tenantId,
            taxYear: input.year
          },
          orderBy: [
            { updatedAt: 'desc' }
          ],
          include: {
            employee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                nationalId: true,
                contact: true
              }
            }
          }
        });

        // Transform employee contact info
        return forms101.map(form => ({
          ...form,
          employee: {
            ...form.employee,
            email: (form.employee.contact as any)?.email || null,
            phone: (form.employee.contact as any)?.phone || null
          }
        }));
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error fetching all Form 101 data"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch Form 101 data",
          cause: error,
        });
      }
    }),

  // Create Form 101
  createForm101: protectedProcedure
    .input(z.object({
      employeeId: z.string().min(1),
      taxYear: z.number(),
      maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED']),
      spouseWorks: z.boolean().default(false),
      childrenCount: z.number().min(0).default(0),
      childrenUnder5: z.number().min(0).default(0),
      childrenUnder18: z.number().min(0).default(0),
      isMainEmployer: z.boolean().default(true),
      hasAdditionalIncome: z.boolean().default(false),
      additionalCreditPoints: z.number().optional(),
      exemptionPercentage: z.number().min(0).max(100).optional(),
      taxCoordinationNumber: z.string().optional(),
      overrideTaxRate: z.number().min(0).max(100).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if employee exists and belongs to user's tenant
        const employee = await db.employee.findFirst({
          where: {
            id: input.employeeId,
            tenantId: user.tenantId
          }
        });

        if (!employee) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employee not found",
          });
        }

        // Check if Form 101 already exists for this year
        const existingForm = await db.form101.findFirst({
          where: {
            employeeId: input.employeeId,
            taxYear: input.taxYear,
            tenantId: user.tenantId
          }
        });

        if (existingForm) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Form 101 already exists for this year",
          });
        }

        // Create Form 101
        const form101 = await db.form101.create({
          data: {
            tenantId: user.tenantId,
            employeeId: input.employeeId,
            taxYear: input.taxYear,
            maritalStatus: input.maritalStatus,
            spouseWorks: input.spouseWorks,
            childrenCount: input.childrenCount,
            childrenUnder5: input.childrenUnder5,
            childrenUnder18: input.childrenUnder18,
            isMainEmployer: input.isMainEmployer,
            hasAdditionalIncome: input.hasAdditionalIncome,
            additionalCreditPoints: input.additionalCreditPoints,
            exemptionPercentage: input.exemptionPercentage,
            taxCoordinationNumber: input.taxCoordinationNumber,
            overrideTaxRate: input.overrideTaxRate,
            // תוקף עד סוף שנת המס (שדה זה הוסר כי הוא לא קיים במודל)
          }
        });

        return form101;
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employeeId: input.employeeId },
          "Error creating Form 101"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create Form 101",
          cause: error,
        });
      }
    }),

  // Update Form 101
  updateForm101: protectedProcedure
    .input(z.object({
      id: z.string().min(1),
      maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED']).optional(),
      spouseWorks: z.boolean().optional(),
      childrenCount: z.number().min(0).optional(),
      childrenUnder5: z.number().min(0).optional(),
      childrenUnder18: z.number().min(0).optional(),
      isMainEmployer: z.boolean().optional(),
      hasAdditionalIncome: z.boolean().optional(),
      additionalCreditPoints: z.number().optional(),
      exemptionPercentage: z.number().min(0).max(100).optional(),
      taxCoordinationNumber: z.string().optional(),
      overrideTaxRate: z.number().min(0).max(100).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get user's tenant
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if Form 101 exists and belongs to user's tenant
        const existingForm = await db.form101.findFirst({
          where: {
            id: input.id,
            tenantId: user.tenantId
          }
        });

        if (!existingForm) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Form 101 not found",
          });
        }

        // Update Form 101
        const { id, ...updateData } = input;
        const form101 = await db.form101.update({
          where: { id },
          data: updateData
        });

        return form101;
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, form101Id: input.id },
          "Error updating Form 101"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update Form 101",
          cause: error,
        });
      }
    }),

  // Send Form 101 for signature
  sendForm101ForSignature: protectedProcedure
    .input(z.object({
      form101Id: z.string().min(1),
      employeeEmail: z.string().email(),
      employeePhone: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Get user's tenant
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true }
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get Form 101 with employee data
      const form101 = await db.form101.findFirst({
        where: {
          id: input.form101Id,
          tenantId: user.tenantId
        },
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
              contact: true
            }
          }
        }
      });

      if (!form101) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form 101 not found",
        });
      }

      // Extract employee contact info
      const contact = form101.employee.contact as any || {};
      const employeeData = {
        firstName: form101.employee.firstName,
        lastName: form101.employee.lastName,
        nationalId: form101.employee.nationalId,
        email: contact.email || null,
        phone: contact.phone || null
      };

      try {
        // Generate PDF document for Form 101
        const documentData = await generateForm101PDF(form101, employeeData);

        // Get signature service
        const signatureService = getSignatureService();

        // Send for signature
        const signatureRequest = await signatureService.sendForSignature({
          form101,
          employeeEmail: input.employeeEmail,
          employeePhone: input.employeePhone,
          documentData
        });

        // Update Form 101 with signature request details
        const updatedForm101 = await db.form101.update({
          where: { id: form101.id },
          data: {
            signatureRequestId: signatureRequest.id,
            signatureRequestSentAt: new Date(),
            signatureRequestExpiresAt: signatureRequest.expiresAt
          }
        });

        return updatedForm101;
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId, form101Id: input.form101Id },
          "Error sending Form 101 for signature"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send Form 101 for signature",
          cause: error,
        });
      }
    }),

  // Check Form 101 signature status
  checkForm101SignatureStatus: protectedProcedure
    .input(z.object({
      form101Id: z.string().min(1)
    }))
    .query(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Get user's tenant
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true }
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get Form 101
      const form101 = await db.form101.findFirst({
        where: {
          id: input.form101Id,
          tenantId: user.tenantId
        }
      });

      if (!form101) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form 101 not found",
        });
      }

      if (!form101.signatureRequestId) {
        return null;
      }

      // Get signature service
      const signatureService = getSignatureService();

      // Check signature status
      const signatureRequest = await signatureService.checkStatus(form101.signatureRequestId);

      // Update Form 101 if signed
      if (signatureRequest.status === 'signed' && !form101.signedAt) {
        await db.form101.update({
          where: { id: form101.id },
          data: {
            signedAt: signatureRequest.signedAt,
            documentUrl: signatureRequest.documentUrl
          }
        });
      }

      return signatureRequest;
    }),

  // Cancel Form 101 signature request
  cancelForm101SignatureRequest: protectedProcedure
    .input(z.object({
      form101Id: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Get user's tenant
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true }
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get Form 101
      const form101 = await db.form101.findFirst({
        where: {
          id: input.form101Id,
          tenantId: user.tenantId
        }
      });

      if (!form101) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form 101 not found",
        });
      }

      if (!form101.signatureRequestId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No active signature request",
        });
      }

      // Get signature service
      const signatureService = getSignatureService();

      // Cancel signature request
      await signatureService.cancelRequest(form101.signatureRequestId);

      // Update Form 101
      const updatedForm101 = await db.form101.update({
        where: { id: form101.id },
        data: {
          signatureRequestId: null,
          signatureRequestSentAt: null,
          signatureRequestExpiresAt: null
        }
      });

      return updatedForm101;
    })
});