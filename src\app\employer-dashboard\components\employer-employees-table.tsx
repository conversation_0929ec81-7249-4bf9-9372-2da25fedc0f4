"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/rtl-components";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, UserPlus, AlertCircle, FileX, CreditCard, FileText } from "lucide-react";
import { useEmployerEmployeesWithDebounce, useCreateEmployee, useEmployeeDocumentStatus } from "../hooks";
import type { EmployeeStatus } from "@prisma/client";
import { useRouter } from "next/navigation";
import { AddEmployeeModal } from "./add-employee-modal";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DocumentUploadModal } from "./employee-documents-manager";
import { toast } from "sonner";
import React from "react";

interface EmployerEmployeesTableProps {
  employerId: string;
}

const statusMap: Record<EmployeeStatus, { label: string; variant: "default" | "destructive" | "secondary" | "outline" }> = {
  ACTIVE: { label: "פעיל", variant: "default" },
  TERMINATED: { label: "הופסק", variant: "destructive" },
  SUSPENDED: { label: "מושעה", variant: "secondary" },
};

// Component to show critical missing documents for an employee
function EmployeeCriticalStatus({ employeeId }: { employeeId: string }) {
  const { criticalMissing, isLoading } = useEmployeeDocumentStatus(employeeId);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [uploadCategory, setUploadCategory] = useState<string>("");
  
  const missingTypes = React.useMemo(() => {
    if (!criticalMissing || criticalMissing.length === 0) {
      return [];
    }
    
    return criticalMissing.map((doc: any) => {
      if (doc.category === 'form-101') return { icon: FileText, label: 'טופס 101', category: 'form-101' };
      if (doc.category === 'id-card-or-passport') return { icon: CreditCard, label: 'תעודת זהות/דרכון', category: 'id-card' };
      if (doc.category === 'id-card' || doc.category === 'passport') return { icon: CreditCard, label: 'תעודת זהות/דרכון', category: doc.category };
      return { icon: FileX, label: doc.displayName, category: doc.category };
    });
  }, [criticalMissing]);
  
  const handleIconClick = React.useCallback((category: string) => {
    setUploadCategory(category);
    setUploadModalOpen(true);
  }, []);
  
  const handleUploadSuccess = React.useCallback(() => {
    toast.success("המסמך הועלה בהצלחה");
    // The document status will refresh automatically via the hook
  }, []);
  
  if (isLoading) {
    return <div className="h-5 w-20 animate-pulse bg-gray-200 rounded" />;
  }
  
  if (missingTypes.length === 0) {
    return null;
  }
  
  return (
    <>
      <TooltipProvider>
        <div className="flex items-center gap-1">
          {missingTypes.map((type, index) => (
            <Tooltip key={index}>
              <TooltipTrigger asChild>
                <button
                  onClick={() => handleIconClick(type.category)}
                  className="flex items-center hover:opacity-70 transition-opacity cursor-pointer"
                >
                  <type.icon className="h-4 w-4 text-red-500" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">חסר: {type.label} - לחץ להעלאה</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      </TooltipProvider>
      
      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        employeeId={employeeId}
        category={uploadCategory}
        onUploadSuccess={handleUploadSuccess}
      />
    </>
  );
}

export function EmployerEmployeesTable({ employerId }: EmployerEmployeesTableProps) {
  const router = useRouter();
  const { 
    employees, 
    totalPages, 
    totalCount, 
    isLoading, 
    isFetching,
    search, 
    setSearch, 
    page, 
    setPage 
  } = useEmployerEmployeesWithDebounce(employerId);
  const { createEmployee, isCreating } = useCreateEmployee(employerId);

  const formatCurrency = (amount: number | string | null | undefined | { toString(): string }) => {
    if (!amount) return "₪0";
    const num = typeof amount === 'string' ? parseFloat(amount) : 
                typeof amount === 'number' ? amount : 
                parseFloat(amount.toString());
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString("he-IL");
  };

  if (isLoading && page === 1) {
    return <EmployerEmployeesTableSkeleton />;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>עובדים ({totalCount})</CardTitle>
          <div className="flex gap-2">
            <div className="relative">
              <Search className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="חיפוש עובדים..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-64 pr-8"
              />
              {isFetching && (
                <div className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                </div>
              )}
            </div>
            <AddEmployeeModal 
              onAddEmployee={createEmployee}
              isLoading={isCreating}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {search.length > 0 && search.length < 2 ? (
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            <p>הקלד לפחות 2 תווים לחיפוש</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">שם מלא</TableHead>
                <TableHead className="text-right">ת.ז.</TableHead>
                <TableHead className="text-right">מחלקה</TableHead>
                <TableHead className="text-right">תאריך התחלה</TableHead>
                <TableHead className="text-right">סטטוס</TableHead>
                <TableHead className="text-right">חוסרים קריטיים</TableHead>
                <TableHead className="text-right">שכר בסיס</TableHead>
                <TableHead className="text-right">תלוש אחרון</TableHead>
                <TableHead className="text-right">פעולות</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {employees.length === 0 && !isLoading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                    {search ? "לא נמצאו עובדים התואמים לחיפוש" : "אין עובדים רשומים"}
                  </TableCell>
                </TableRow>
              ) : (
                employees.map((employee: any) => (
              <TableRow key={employee.id} className="hover:bg-muted/50">
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {employee.profilePictureUrl && (
                      <img
                        src={employee.profilePictureUrl}
                        alt={employee.fullName}
                        className="h-8 w-8 rounded-full object-cover"
                      />
                    )}
                    <div>
                      <div>{employee.fullName}</div>
                      {employee.email && (
                        <div className="text-xs text-muted-foreground">
                          {employee.email}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{employee.nationalId}</TableCell>
                <TableCell>
                  {employee.department?.name || "-"}
                </TableCell>
                <TableCell>{formatDate(employee.startDate)}</TableCell>
                <TableCell>
                  <Badge variant={statusMap[employee.status as EmployeeStatus]?.variant || "default"}>
                    {statusMap[employee.status as EmployeeStatus]?.label || employee.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <EmployeeCriticalStatus employeeId={employee.id} />
                </TableCell>
                <TableCell>{formatCurrency(employee.baseSalary)}</TableCell>
                <TableCell>
                  {employee.latestPayslip ? (
                    <div className="flex items-center gap-2">
                      <div>
                        <div className="text-sm">{employee.latestPayslip.period}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(employee.latestPayslip.netSalary)}
                        </div>
                      </div>
                      {employee.latestPayslip.status === 'DRAFT' && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertCircle className="h-4 w-4 text-orange-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-sm">תלוש לא אושר</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => router.push(`/employer-dashboard/employees/${employee.id}`)}
                  >
                    הצג פרטים
                                    </Button>
                </TableCell>
              </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        )}
        
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-2 py-4">
            <div className="text-sm text-muted-foreground">
              עמוד {page} מתוך {totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                הקודם
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                הבא
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function EmployerEmployeesTableSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-16 w-full" />
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 