"use client";

import { useSession } from "next-auth/react";
import { redirect, usePara<PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/rtl-components";
import { ArrowRight, Save, Edit2, X } from "lucide-react";
import { useEmployeeDetails, useUpdateEmployee, useEmployeePayslips, useEmployeeSalaryTransactions, usePayslipDetails } from "../../hooks";
import { EmployeeDocumentsManager } from "../../components/employee-documents-manager";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { EmployeeStatus } from "@prisma/client";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PayslipDetailsModal } from "../../components/payslip-details-modal";

const statusOptions: { value: EmployeeStatus; label: string }[] = [
  { value: "ACTIVE", label: "פעיל" },
  { value: "TERMINATED", label: "הופסק" },
  { value: "SUSPENDED", label: "מושעה" },
];

export default function EmployeeDetailsPage() {
  const { data: session, status: sessionStatus } = useSession();
  const params = useParams();
  const router = useRouter();
  const employeeId = params?.id as string;
  
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [payslipsPage, setPayslipsPage] = useState(1);
  const [transactionsPage, setTransactionsPage] = useState(1);
  const [transactionFilter, setTransactionFilter] = useState<{ year?: number; month?: number }>({});
  const [selectedPayslipId, setSelectedPayslipId] = useState<string | null>(null);
  
  const { employee, isLoading } = useEmployeeDetails(employeeId);
  const { updateEmployee, isUpdating } = useUpdateEmployee();
  const { payslips, totalPages: payslipsTotalPages, isLoading: payslipsLoading } = useEmployeePayslips(employeeId, payslipsPage);
  const { transactions, totalPages: transactionsTotalPages, isLoading: transactionsLoading } = useEmployeeSalaryTransactions(employeeId, transactionFilter, transactionsPage);
  const { payslip: selectedPayslip, isLoading: payslipDetailsLoading } = usePayslipDetails(selectedPayslipId || "");

  const handleSave = async () => {
    if (!employee) return;
    
    try {
      await updateEmployee({
        id: employeeId,
        ...formData,
      });
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to update employee:", error);
    }
  };

  const handleCancel = () => {
    setFormData({});
    setIsEditing(false);
  };

  const formatCurrency = (amount: number | string | null | undefined | { toString(): string }) => {
    if (!amount) return "₪0";
    const num = typeof amount === 'string' ? parseFloat(amount) : 
                typeof amount === 'number' ? amount : 
                parseFloat(amount.toString());
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return "-";
    return new Date(date).toLocaleDateString("he-IL");
  };

  if (sessionStatus === "loading" || isLoading) {
    return <EmployeeDetailsPageSkeleton />;
  }

  if (!session) {
    redirect("/login");
  }

  if (!employee) {
    return (
      <div className="flex min-h-screen w-full flex-col items-center justify-center gap-4 p-6">
        <h1 className="text-2xl font-bold">עובד לא נמצא</h1>
        <Button onClick={() => router.back()}>
          <ArrowRight className="h-4 w-4 ml-2" />
          חזרה
        </Button>
      </div>
    );
  }

  const currentFormData = { ...employee, ...formData };

  return (
    <div className="flex min-h-screen w-full flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowRight className="h-4 w-4 ml-2" />
            חזרה
          </Button>
          <h1 className="text-3xl font-bold">{employee.fullName}</h1>
          <Badge variant={employee.status === "ACTIVE" ? "default" : "secondary"}>
            {statusOptions.find(s => s.value === employee.status)?.label}
          </Badge>
        </div>
        <div className="flex gap-2">
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit2 className="h-4 w-4 ml-2" />
              עריכה
            </Button>
          ) : (
            <>
              <Button variant="outline" onClick={handleCancel} disabled={isUpdating}>
                <X className="h-4 w-4 ml-2" />
                ביטול
              </Button>
              <Button onClick={handleSave} disabled={isUpdating}>
                <Save className="h-4 w-4 ml-2" />
                שמירה
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="personal">פרטים אישיים</TabsTrigger>
          <TabsTrigger value="employment">פרטי העסקה</TabsTrigger>
          <TabsTrigger value="salary">שכר והטבות</TabsTrigger>
          <TabsTrigger value="payslips">תלושים</TabsTrigger>
          <TabsTrigger value="transactions">מרכיבי שכר</TabsTrigger>
          <TabsTrigger value="documents">מסמכים</TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>פרטים אישיים</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>שם פרטי</Label>
                {isEditing ? (
                  <Input
                    value={currentFormData.firstName || ""}
                    onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{employee.firstName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>שם משפחה</Label>
                {isEditing ? (
                  <Input
                    value={currentFormData.lastName || ""}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{employee.lastName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>מספר זהות</Label>
                <p className="text-sm">{employee.nationalId}</p>
              </div>

              <div className="space-y-2">
                <Label>תאריך לידה</Label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={currentFormData.birthDate ? new Date(currentFormData.birthDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => setFormData({ ...formData, birthDate: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{formatDate(employee.birthDate)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>אימייל</Label>
                {isEditing ? (
                  <Input
                    type="email"
                    value={currentFormData.email || ""}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{employee.email || "-"}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>טלפון</Label>
                {isEditing ? (
                  <Input
                    value={currentFormData.phone || ""}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{employee.phone || "-"}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>פרטי העסקה</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>סטטוס</Label>
                {isEditing ? (
                  <Select
                    value={currentFormData.status}
                    onValueChange={(value) => setFormData({ ...formData, status: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-sm">{statusOptions.find(s => s.value === employee.status)?.label}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>מחלקה</Label>
                <p className="text-sm">{employee.department?.name || "-"}</p>
              </div>

              <div className="space-y-2">
                <Label>תאריך התחלה</Label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={currentFormData.startDate ? new Date(currentFormData.startDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  />
                ) : (
                  <p className="text-sm">{formatDate(employee.startDate)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>תאריך סיום</Label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={currentFormData.endDate ? new Date(currentFormData.endDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value || null })}
                  />
                ) : (
                  <p className="text-sm">{formatDate(employee.endDate)}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="salary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>שכר והטבות</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>שכר בסיס</Label>
                {isEditing ? (
                  <Input
                    type="number"
                    value={currentFormData.baseSalary || ""}
                    onChange={(e) => setFormData({ ...formData, baseSalary: parseFloat(e.target.value) || 0 })}
                  />
                ) : (
                  <p className="text-sm">{formatCurrency(employee.baseSalary)}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>החזר נסיעות</Label>
                {isEditing ? (
                  <Input
                    type="number"
                    value={currentFormData.travelAllowance || ""}
                    onChange={(e) => setFormData({ ...formData, travelAllowance: parseFloat(e.target.value) || 0 })}
                  />
                ) : (
                  <p className="text-sm">{formatCurrency(employee.travelAllowance)}</p>
                )}
              </div>

              {employee.latestPayslip && (
                <>
                  <div className="space-y-2">
                    <Label>תלוש אחרון</Label>
                    <p className="text-sm">{employee.latestPayslip.period}</p>
                  </div>

                  <div className="space-y-2">
                    <Label>נטו בתלוש אחרון</Label>
                    <p className="text-sm">{formatCurrency(employee.latestPayslip.netSalary)}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payslips" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>תלושי שכר</CardTitle>
            </CardHeader>
            <CardContent>
              {payslipsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Skeleton key={index} className="h-16 w-full" />
                  ))}
                </div>
              ) : payslips.length === 0 ? (
                <p className="text-sm text-muted-foreground">לא נמצאו תלושי שכר</p>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">תקופה</TableHead>
                        <TableHead className="text-right">סטטוס</TableHead>
                        <TableHead className="text-right">ברוטו</TableHead>
                        <TableHead className="text-right">נטו</TableHead>
                        <TableHead className="text-right">מס הכנסה</TableHead>
                        <TableHead className="text-right">ביטוח לאומי</TableHead>
                        <TableHead className="text-right">רכיבים</TableHead>
                        <TableHead className="text-right">פעולות</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {payslips.map((payslip) => (
                        <TableRow key={payslip.id}>
                          <TableCell className="font-medium">{payslip.period}</TableCell>
                          <TableCell>
                            <Badge variant={payslip.status === "PAID" || payslip.status === "APPROVED" ? "default" : "secondary"}>
                              {payslip.status === "DRAFT" ? "טיוטה" : 
                               payslip.status === "CALCULATED" ? "מחושב" :
                               payslip.status === "APPROVED" ? "מאושר" :
                               payslip.status === "PAID" ? "שולם" : "מבוטל"}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatCurrency(payslip.grossPay)}</TableCell>
                          <TableCell>{formatCurrency(payslip.netPay)}</TableCell>
                          <TableCell>{formatCurrency(payslip.taxDeducted)}</TableCell>
                          <TableCell>{formatCurrency(payslip.insuranceDeducted)}</TableCell>
                          <TableCell>{payslip.itemsCount} רכיבים</TableCell>
                          <TableCell>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => setSelectedPayslipId(payslip.id)}
                            >
                              הצג
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {payslipsTotalPages > 1 && (
                    <div className="flex items-center justify-between px-2 py-4">
                      <div className="text-sm text-muted-foreground">
                        עמוד {payslipsPage} מתוך {payslipsTotalPages}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPayslipsPage(payslipsPage - 1)}
                          disabled={payslipsPage === 1}
                        >
                          הקודם
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPayslipsPage(payslipsPage + 1)}
                          disabled={payslipsPage === payslipsTotalPages}
                        >
                          הבא
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>מרכיבי שכר</CardTitle>
                <div className="flex gap-2">
                  <Select
                    value={transactionFilter.year?.toString() || "all"}
                    onValueChange={(value) => {
                      setTransactionFilter({ ...transactionFilter, year: value === "all" ? undefined : parseInt(value) });
                      setTransactionsPage(1);
                    }}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="שנה" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">כל השנים</SelectItem>
                      {[2024, 2023, 2022].map((year) => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={transactionFilter.month?.toString() || "all"}
                    onValueChange={(value) => {
                      setTransactionFilter({ ...transactionFilter, month: value === "all" ? undefined : parseInt(value) });
                      setTransactionsPage(1);
                    }}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="חודש" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">כל החודשים</SelectItem>
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                        <SelectItem key={month} value={month.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {transactionsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Skeleton key={index} className="h-12 w-full" />
                  ))}
                </div>
              ) : transactions.length === 0 ? (
                <p className="text-sm text-muted-foreground">לא נמצאו מרכיבי שכר</p>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">תקופה</TableHead>
                        <TableHead className="text-right">תיאור</TableHead>
                        <TableHead className="text-right">קוד</TableHead>
                        <TableHead className="text-right">מחלקה</TableHead>
                        <TableHead className="text-right">כמות</TableHead>
                        <TableHead className="text-right">תעריף</TableHead>
                        <TableHead className="text-right">סכום</TableHead>
                        <TableHead className="text-right">עובד</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell>{transaction.period}</TableCell>
                          <TableCell>{transaction.description || "-"}</TableCell>
                          <TableCell>{transaction.componentCode || "-"}</TableCell>
                          <TableCell>{transaction.department?.name || "-"}</TableCell>
                          <TableCell>{transaction.quantity ? transaction.quantity.toString() : "-"}</TableCell>
                          <TableCell>{transaction.rate ? formatCurrency(transaction.rate) : "-"}</TableCell>
                          <TableCell>{transaction.amount ? formatCurrency(transaction.amount) : "-"}</TableCell>
                          <TableCell>
                            <Badge variant={transaction.isProcessed ? "default" : "secondary"}>
                              {transaction.isProcessed ? "עובד" : "ממתין"}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {transactionsTotalPages > 1 && (
                    <div className="flex items-center justify-between px-2 py-4">
                      <div className="text-sm text-muted-foreground">
                        עמוד {transactionsPage} מתוך {transactionsTotalPages}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setTransactionsPage(transactionsPage - 1)}
                          disabled={transactionsPage === 1}
                        >
                          הקודם
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setTransactionsPage(transactionsPage + 1)}
                          disabled={transactionsPage === transactionsTotalPages}
                        >
                          הבא
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <EmployeeDocumentsManager
            employeeId={employeeId}
            employeeName={employee.fullName}
            employeeNationalId={employee.nationalId}
          />
        </TabsContent>
      </Tabs>

      <PayslipDetailsModal
        payslip={selectedPayslip}
        isOpen={!!selectedPayslipId && !payslipDetailsLoading}
        onClose={() => setSelectedPayslipId(null)}
      />
    </div>
  );
}

function EmployeeDetailsPageSkeleton() {
  return (
    <div className="flex min-h-screen w-full flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-6 w-20" />
        </div>
        <Skeleton className="h-10 w-24" />
      </div>

      <div className="space-y-4">
        <Skeleton className="h-10 w-full max-w-md" />
        <Skeleton className="h-96 w-full" />
      </div>
    </div>
  );
} 