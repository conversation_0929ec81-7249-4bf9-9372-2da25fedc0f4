"use client";

import React, { useState, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { 
  Upload, 
  FileText, 
  Download, 
  Trash2, 
  Eye, 
  Edit, 
  IdCard,
  CreditCard,
  Heart,
  GraduationCap,
  File,
  Calendar,
  User,
  Plus,
  Plane,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON><PERSON><PERSON>,
  Clock
} from "lucide-react";
import {
  useEmployeeDocuments,
  useEmployeeForm101Documents,
  useEmployeeIdCardDocuments,
  useUploadEmployeeDocument,
  useDeleteEmployeeDocument,
  useDownloadEmployeeDocument,
  useUpdateEmployeeDocumentMetadata,
  useEmployeeDetails,
  useEmployeeDocumentStatus
} from "../hooks";
import { 
  DOCUMENT_CATEGORY_CONFIG, 
  DOCUMENT_REQUIREMENT_LEVELS,
  getRequiredDocumentCategories,
  getOptionalDocumentCategories,
  getMissingRequiredDocuments,
  calculateDocumentCompletionPercentage,
  getDocumentRequirementLevel
} from "../cache-config";

interface EmployeeDocumentsManagerProps {
  employeeId: string;
  employeeName: string;
  employeeNationalId: string;
}

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeId: string;
  category: string;
  onUploadSuccess: () => void;
}

interface DocumentEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  document: any;
  employeeId: string;
  onUpdateSuccess: () => void;
}

const categoryIcons = {
  'id-card': IdCard,
  'passport': IdCard,
  'form-101': FileText,
  'contract': FileText,
  'bank-details': CreditCard,
  'medical': Heart,
  'education': GraduationCap,
  'visa': Plane,
  'other': File
};

export function DocumentUploadModal({ isOpen, onClose, employeeId, category, onUploadSuccess }: DocumentUploadModalProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [year, setYear] = useState<number | undefined>();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  
  const { uploadDocument, isUploading, error } = useUploadEmployeeDocument(employeeId);
  const categoryConfig = DOCUMENT_CATEGORY_CONFIG[category as keyof typeof DOCUMENT_CATEGORY_CONFIG];

  const [isDragActive, setIsDragActive] = useState(false);
  
  // Check if this is ID card or passport
  const isIdDocument = category === 'id-card' || category === 'passport';
  // Allow multiple files only for ID documents
  const allowMultiple = isIdDocument;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      if (allowMultiple) {
        setSelectedFiles(fileArray);
      } else {
        const firstFile = fileArray[0];
        if (firstFile) {
          setSelectedFiles([firstFile]);
          if (!title && !isIdDocument) {
            setTitle(firstFile.name);
          }
        }
      }
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragActive(false);
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      if (allowMultiple) {
        setSelectedFiles(fileArray);
      } else {
        const firstFile = fileArray[0];
        if (firstFile) {
          setSelectedFiles([firstFile]);
          if (!title && !isIdDocument) {
            setTitle(firstFile.name);
          }
        }
      }
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast.error("אנא בחר קובץ להעלאה");
      return;
    }

    // בדיקה שתיאור נדרש למסמכים מסוג "other"
    if (categoryConfig?.allowCustomDescription && !description.trim() && !isIdDocument) {
      toast.error("אנא הוסף תיאור למסמך");
      return;
    }

    try {
      // Upload all files
      const uploadPromises = selectedFiles.map(async (file) => {
        const fileTitle = isIdDocument ? file.name : (title || file.name);
        const fileDescription = isIdDocument ? '' : description;
        
        return uploadDocument(file, category, {
          title: fileTitle,
          description: fileDescription,
          year
        });
      });
      
      await Promise.all(uploadPromises);
      
      toast.success(selectedFiles.length > 1 ? "הקבצים הועלו בהצלחה" : "הקובץ הועלה בהצלחה");
      onUploadSuccess();
      onClose();
      
      // Reset form
      setTitle("");
      setDescription("");
      setYear(undefined);
      setSelectedFiles([]);
    } catch (error) {
      toast.error("שגיאה בהעלאת הקובץ");
      console.error("Upload error:", error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>העלאת {categoryConfig?.displayName}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* File Upload Area */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => document.getElementById('file-input')?.click()}
            className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-primary'
            }`}
          >
            <input
              id="file-input"
              type="file"
              onChange={handleFileSelect}
              accept={categoryConfig?.allowedTypes.join(',')}
              multiple={allowMultiple}
              className="hidden"
            />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {selectedFiles.length === 0 ? (
              <div>
                <p className="text-sm">
                  {allowMultiple ? "גרור קבצים לכאן או לחץ לבחירה" : "גרור קובץ לכאן או לחץ לבחירה"}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  קבצים מותרים: {categoryConfig?.allowedTypes.join(', ')}
                </p>
                {allowMultiple && (
                  <p className="text-xs text-gray-500 mt-1">ניתן להעלות מספר קבצים</p>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex-1 text-right">
                      <p className="font-medium">{file.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                    {allowMultiple && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedFiles(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="ml-2 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Title - only show for non-ID documents */}
          {!isIdDocument && (
            <div>
              <Label htmlFor="title">כותרת</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="כותרת המסמך"
              />
            </div>
          )}

          {/* Year (for Form 101 and other yearly documents) */}
          {(category === 'form-101' || category === 'contract') && (
            <div>
              <Label htmlFor="year">שנה</Label>
              <Input
                id="year"
                type="number"
                value={year || ''}
                onChange={(e) => setYear(e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="שנה"
                min="2000"
                max={new Date().getFullYear() + 1}
              />
            </div>
          )}

          {/* Description - only show for non-ID documents */}
          {!isIdDocument && (
            <div>
              <Label htmlFor="description">
                {categoryConfig?.allowCustomDescription ? 'תיאור המסמך (חובה)' : 'תיאור (אופציונלי)'}
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={categoryConfig?.allowCustomDescription ? 'תאר את המסמך...' : 'תיאור המסמך'}
                rows={3}
                required={categoryConfig?.allowCustomDescription}
              />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="text-red-600 text-sm">
              שגיאה: {error.message}
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={onClose}>
              ביטול
            </Button>
            <Button 
              onClick={handleUpload} 
              disabled={selectedFiles.length === 0 || isUploading}
            >
              {isUploading ? "מעלה..." : selectedFiles.length > 1 ? `העלה ${selectedFiles.length} קבצים` : "העלה"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function DocumentEditModal({ isOpen, onClose, document, employeeId, onUpdateSuccess }: DocumentEditModalProps) {
  const [title, setTitle] = useState(document?.title || "");
  const [description, setDescription] = useState((document?.metadata as any)?.description || "");
  const [year, setYear] = useState<number | undefined>((document?.metadata as any)?.year);
  
  const { updateDocumentMetadata, isUpdating } = useUpdateEmployeeDocumentMetadata(employeeId);

  React.useEffect(() => {
    if (document) {
      setTitle(document.title || "");
      setDescription((document.metadata as any)?.description || "");
      setYear((document.metadata as any)?.year);
    }
  }, [document]);

  const handleUpdate = () => {
    updateDocumentMetadata({
      documentId: document.id,
      employeeId,
      title,
      description,
      year
    }, {
      onSuccess: () => {
        toast.success("המסמך עודכן בהצלחה");
        onUpdateSuccess();
        onClose();
      },
      onError: () => {
        toast.error("שגיאה בעדכון המסמך");
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>עריכת מסמך</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="edit-title">כותרת</Label>
            <Input
              id="edit-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="כותרת המסמך"
            />
          </div>

          <div>
            <Label htmlFor="edit-year">שנה</Label>
            <Input
              id="edit-year"
              type="number"
              value={year || ''}
              onChange={(e) => setYear(e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="שנה"
              min="2000"
              max={new Date().getFullYear() + 1}
            />
          </div>

          <div>
            <Label htmlFor="edit-description">תיאור</Label>
            <Textarea
              id="edit-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="תיאור המסמך"
              rows={3}
            />
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={onClose}>
              ביטול
            </Button>
            <Button onClick={handleUpdate} disabled={isUpdating}>
              {isUpdating ? "מעדכן..." : "עדכן"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function DocumentStatusOverview({ employeeId }: { employeeId: string }) {
  const { 
    missingDocuments, 
    completionPercentage, 
    criticalMissing, 
    optionalMissing, 
    isLoading 
  } = useEmployeeDocumentStatus(employeeId);
  
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-4 w-32" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${criticalMissing.length > 0 ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {criticalMissing.length === 0 ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-red-600" />
            )}
            <h3 className="font-medium">
              {criticalMissing.length === 0 ? 'כל המסמכים החובה קיימים' : 'מסמכים חובה חסרים'}
            </h3>
          </div>
          <div className="text-sm font-medium">
            {completionPercentage}% הושלם
          </div>
        </div>
        
        {criticalMissing.length > 0 && (
          <div className="space-y-2">
            <div>
              <p className="text-sm font-medium text-red-700 mb-1">חובה מיידית:</p>
              <div className="flex flex-wrap gap-1">
                {criticalMissing.map((doc, index) => (
                  <Badge key={index} variant="destructive" className="text-xs">
                    {doc.displayName}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {optionalMissing.length > 0 && criticalMissing.length === 0 && (
          <div className="mt-2">
            <p className="text-sm text-gray-600">
              יש {optionalMissing.length} מסמכים אופציונליים שניתן להוסיף
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function DocumentCard({ document, employeeId, onEdit, onRefresh }: any) {
  const { deleteDocument, isDeleting } = useDeleteEmployeeDocument(employeeId);
  const { downloadDocument, isDownloading } = useDownloadEmployeeDocument();
  
  const categoryConfig = DOCUMENT_CATEGORY_CONFIG[document.category as keyof typeof DOCUMENT_CATEGORY_CONFIG];
  const IconComponent = categoryIcons[document.category as keyof typeof categoryIcons] || File;
  const requirementLevel = getDocumentRequirementLevel(document.category);

  const handleDelete = () => {
    if (confirm("האם אתה בטוח שברצונך למחוק את המסמך?")) {
      deleteDocument({
        documentId: document.id,
        employeeId
      }, {
        onSuccess: () => {
          toast.success("המסמך נמחק בהצלחה");
          onRefresh();
        },
        onError: () => {
          toast.error("שגיאה במחיקת המסמך");
        }
      });
    }
  };

  const handleDownload = () => {
    downloadDocument(document.id, employeeId).catch(() => {
      toast.error("שגיאה בהורדת המסמך");
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('he-IL');
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <IconComponent className="h-8 w-8 text-blue-600 mt-1" />
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm truncate">{document.title}</h4>
              <p className="text-xs text-gray-500 mt-1">{document.fileName}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={requirementLevel.badgeVariant} className="text-xs">
                  {categoryConfig?.displayName}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {requirementLevel.label}
                </Badge>
                <span className="text-xs text-gray-500">
                  {formatFileSize(document.fileSize)}
                </span>
              </div>
              {(document.metadata as any)?.year && (
                <div className="flex items-center gap-1 mt-1">
                  <Calendar className="h-3 w-3 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {(document.metadata as any).year}
                  </span>
                </div>
              )}
              <p className="text-xs text-gray-400 mt-1">
                הועלה: {formatDate(document.uploadedAt)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDownload}
              disabled={isDownloading}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(document)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {(document.metadata as any)?.description && (
          <p className="text-xs text-gray-600 mt-2 line-clamp-2">
            {(document.metadata as any).description}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

export function EmployeeDocumentsManager({ 
  employeeId, 
  employeeName, 
  employeeNationalId 
}: EmployeeDocumentsManagerProps) {
  const [activeTab, setActiveTab] = useState("status");
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [uploadCategory, setUploadCategory] = useState("other");
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingDocument, setEditingDocument] = useState<any>(null);
  const [selectedYear, setSelectedYear] = useState<number | undefined>();

  // Get employee details for start date
  const { employee: employeeDetails } = useEmployeeDetails(employeeId);

  // Hooks for different document types
  const { 
    documents: allDocuments, 
    isLoading: isLoadingAll, 
    error: documentsError,
    refetch: refetchAll 
  } = useEmployeeDocuments(employeeId);
  
  const { 
    documents: form101Documents, 
    documentsByYear: form101ByYear,
    availableYears,
    isLoading: isLoadingForm101,
    error: form101Error
  } = useEmployeeForm101Documents(employeeId, selectedYear);
  
  const { 
    employee,
    documents: idCardDocuments, 
    isLoading: isLoadingIdCards,
    error: idCardError
  } = useEmployeeIdCardDocuments(employeeId);

  // If there are database errors, show them with a friendly message
  const hasErrors = documentsError || form101Error || idCardError;
  
  // Handle database schema error
  React.useEffect(() => {
    if (hasErrors) {
      // If it's likely a schema error
      if (documentsError?.message?.includes('column') || 
          form101Error?.message?.includes('column') || 
          idCardError?.message?.includes('column')) {
        toast.error(
          "נראה שיש בעיה במבנה בסיס הנתונים. יש לפנות למנהל המערכת.", 
          { id: "schema-error", duration: 5000 }
        );
      }
    }
  }, [documentsError, form101Error, idCardError, hasErrors]);

  const handleUploadSuccess = () => {
    refetchAll();
  };

  const handleEditDocument = (document: any) => {
    setEditingDocument(document);
    setEditModalOpen(true);
  };

  const handleUpdateSuccess = () => {
    refetchAll();
  };

  const openUploadModal = (category: string) => {
    setUploadCategory(category);
    setUploadModalOpen(true);
  };

  // Get required and optional categories for organized display
  const requiredCategories = getRequiredDocumentCategories();
  const optionalCategories = getOptionalDocumentCategories();

  // Render error state if necessary
  if (hasErrors) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-12">
            <AlertTriangle className="mx-auto h-12 w-12 text-amber-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              שגיאה בטעינת המסמכים
            </h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              לא ניתן לטעון את המסמכים כרגע. ייתכן שקיימת בעיה במבנה בסיס הנתונים. 
              אנא נסה שוב מאוחר יותר או פנה לתמיכה הטכנית.
            </p>
            <Button onClick={() => refetchAll()} variant="outline">
              נסה שוב
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderDocumentGrid = (documents: any[]) => {
    if (documents.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <File className="mx-auto h-12 w-12 mb-4 text-gray-300" />
          <p>אין מסמכים להצגה</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {documents.map((document) => (
          <DocumentCard
            key={document.id}
            document={document}
            employeeId={employeeId}
            onEdit={handleEditDocument}
            onRefresh={refetchAll}
          />
        ))}
      </div>
    );
  };

  const renderLoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Skeleton className="h-8 w-8" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-3 w-1/4" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                מסמכי {employeeName}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                ת.ז: {employeeNationalId}
              </p>
            </div>
            <Button onClick={() => openUploadModal("other")}>
              <Plus className="h-4 w-4 mr-2" />
              העלה מסמך
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Document Status Overview */}
      <DocumentStatusOverview 
        employeeId={employeeId} 
      />

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="status">סטטוס</TabsTrigger>
          <TabsTrigger value="required">מסמכים חובה</TabsTrigger>
          <TabsTrigger value="form-101">טופס 101</TabsTrigger>
          <TabsTrigger value="optional">אופציונליים</TabsTrigger>
          <TabsTrigger value="all">כל המסמכים</TabsTrigger>
        </TabsList>

        {/* Status Overview */}
        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Required Documents Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  מסמכים חובה
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* תעודת זהות או דרכון */}
                <div className="p-3 rounded-lg border border-red-200 bg-red-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <IdCard className="h-5 w-5 text-red-600" />
                      <div>
                        <p className="font-medium text-sm">תעודת זהות או דרכון</p>
                        <p className="text-xs text-gray-500">נדרש אחד מהמסמכים</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {(allDocuments.some(doc => doc.category === 'id-card') || 
                        allDocuments.some(doc => doc.category === 'passport')) ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <div className="flex gap-1">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => openUploadModal('id-card')}
                          >
                            תז
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => openUploadModal('passport')}
                          >
                            דרכון
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* טופס 101 לשנה הנוכחית */}
                <div className="p-3 rounded-lg border border-red-200 bg-red-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-red-600" />
                      <div>
                        <p className="font-medium text-sm">טופס 101 {new Date().getFullYear()}</p>
                        <p className="text-xs text-gray-500">טופס 101 לשנה הנוכחית</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {allDocuments.some(doc => 
                        doc.category === 'form-101' && 
                        ((doc.metadata as any)?.year === new Date().getFullYear() || 
                         new Date(doc.uploadedAt).getFullYear() === new Date().getFullYear())
                      ) ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => openUploadModal('form-101')}
                        >
                          העלה
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Optional Documents Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <File className="h-5 w-5 text-blue-600" />
                  מסמכים אופציונליים
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {optionalCategories.map(({ category, displayName, description }) => {
                  const documentsCount = allDocuments.filter(doc => doc.category === category).length;
                  const IconComponent = categoryIcons[category as keyof typeof categoryIcons] || File;
                  
                  return (
                    <div key={category} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <IconComponent className="h-5 w-5" />
                        <div>
                          <p className="font-medium text-sm">{displayName}</p>
                          <p className="text-xs text-gray-500">{description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {documentsCount > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {documentsCount}
                          </Badge>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => openUploadModal(category)}
                        >
                          הוסף
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Required Documents */}
        <TabsContent value="required" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">מסמכים חובה</h3>
          </div>
          {isLoadingAll ? renderLoadingSkeleton() : renderDocumentGrid(
            allDocuments.filter(doc => {
              // רק תז, דרכון וטופס 101
              return doc.category && ['id-card', 'passport', 'form-101'].includes(doc.category);
            })
          )}
        </TabsContent>

        {/* Optional Documents */}
        <TabsContent value="optional" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">מסמכים אופציונליים</h3>
            <Button 
              variant="outline" 
              onClick={() => openUploadModal("other")}
            >
              <Plus className="h-4 w-4 mr-2" />
              הוסף מסמך
            </Button>
          </div>
          {isLoadingAll ? renderLoadingSkeleton() : renderDocumentGrid(
            allDocuments.filter(doc => {
              // כל מה שלא תז, דרכון או טופס 101
              return doc.category && !['id-card', 'passport', 'form-101'].includes(doc.category);
            })
          )}
        </TabsContent>

        {/* All Documents */}
        <TabsContent value="all" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">כל המסמכים</h3>
            <Button 
              variant="outline" 
              onClick={() => openUploadModal("other")}
            >
              <Plus className="h-4 w-4 mr-2" />
              הוסף מסמך
            </Button>
          </div>
          {isLoadingAll ? renderLoadingSkeleton() : renderDocumentGrid(allDocuments)}
        </TabsContent>

        {/* Form 101 Documents */}
        <TabsContent value="form-101" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-medium">טפסי 101</h3>
              {availableYears.length > 0 && (
                <Select 
                  value={selectedYear?.toString() || "all"} 
                  onValueChange={(value) => setSelectedYear(value === "all" ? undefined : parseInt(value))}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="שנה" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">כל השנים</SelectItem>
                    {availableYears.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
            <Button 
              variant="outline" 
              onClick={() => openUploadModal("form-101")}
            >
              <FileText className="h-4 w-4 mr-2" />
              הוסף טופס 101
            </Button>
          </div>
          
          {isLoadingForm101 ? renderLoadingSkeleton() : (
            selectedYear ? (
              renderDocumentGrid(form101ByYear[selectedYear] || [])
            ) : (
              <div className="space-y-6">
                {Object.entries(form101ByYear)
                  .sort(([a], [b]) => parseInt(b) - parseInt(a))
                  .map(([year, documents]) => (
                    <div key={year}>
                      <h4 className="text-md font-medium mb-3 flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        שנת {year}
                      </h4>
                      {renderDocumentGrid(documents)}
                    </div>
                  ))}
                {Object.keys(form101ByYear).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="mx-auto h-12 w-12 mb-4 text-gray-300" />
                    <p>אין טפסי 101 להצגה</p>
                  </div>
                )}
              </div>
            )
          )}
        </TabsContent>


      </Tabs>

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        employeeId={employeeId}
        category={uploadCategory}
        onUploadSuccess={handleUploadSuccess}
      />

      {/* Edit Modal */}
      <DocumentEditModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        document={editingDocument}
        employeeId={employeeId}
        onUpdateSuccess={handleUpdateSuccess}
      />
    </div>
  );
} 