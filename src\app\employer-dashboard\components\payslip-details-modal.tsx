"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface PayslipDetailsModalProps {
  payslip: any;
  isOpen: boolean;
  onClose: () => void;
}

export function PayslipDetailsModal({ payslip, isOpen, onClose }: PayslipDetailsModalProps) {
  if (!payslip) return null;

  const formatCurrency = (amount: number | string | null | undefined | { toString(): string }) => {
    if (!amount) return "₪0";
    const num = typeof amount === 'string' ? parseFloat(amount) : 
                typeof amount === 'number' ? amount : 
                parseFloat(amount.toString());
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  const earnings = payslip.items?.filter((item: any) => item.type === "EARNING") || [];
  const deductions = payslip.items?.filter((item: any) => item.type === "DEDUCTION") || [];
  const employerContribs = payslip.items?.filter((item: any) => item.type === "EMPLOYER_CONTRIB") || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>תלוש שכר - {payslip.period}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">סיכום</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>ברוטו:</span>
                  <span className="font-medium">{formatCurrency(payslip.grossPay)}</span>
                </div>
                <div className="flex justify-between">
                  <span>מס הכנסה:</span>
                  <span className="font-medium">{formatCurrency(payslip.taxDeducted)}</span>
                </div>
                <div className="flex justify-between">
                  <span>ביטוח לאומי:</span>
                  <span className="font-medium">{formatCurrency(payslip.insuranceDeducted)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span>נטו:</span>
                  <span>{formatCurrency(payslip.netPay)}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">פרטים</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>סטטוס:</span>
                  <Badge variant={payslip.status === "PAID" || payslip.status === "APPROVED" ? "default" : "secondary"}>
                    {payslip.status === "DRAFT" ? "טיוטה" : 
                     payslip.status === "CALCULATED" ? "מחושב" :
                     payslip.status === "APPROVED" ? "מאושר" :
                     payslip.status === "PAID" ? "שולם" : "מבוטל"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>תאריך הפקה:</span>
                  <span>{new Date(payslip.issuedAt).toLocaleDateString("he-IL")}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Earnings */}
          {earnings.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">תשלומים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">כמות</TableHead>
                    <TableHead className="text-right">תעריף</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {earnings.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{item.units ? item.units.toString() : "-"}</TableCell>
                      <TableCell>{item.rate ? formatCurrency(item.rate) : "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Deductions */}
          {deductions.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">ניכויים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deductions.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Employer Contributions */}
          {employerContribs.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">הפרשות מעסיק</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employerContribs.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 