import {
  PrismaClient,
  Role,
  Sector,
  AgreementType,
  PayslipStatus,
  PayslipItemKod,
  AlertType,
  AlertCategory,
  MaritalStatus,
} from "@prisma/client";
import bcrypt from "bcryptjs";
import {
  calculateOvertimePay,
  calculateIncomeTax,
  calculateNationalInsurance,
  calculateForeignWorkerDeductions,
  validateMinimumWageCompliance,
  checkVisaExpiryAlerts,
} from "../src/utils/payroll-calculations";
import {
  generateVisaExpiryAlerts,
  generateMissingForm101Alerts,
  generateOvertimeLimitAlerts,
  generateDepositComplianceAlerts,
} from "../src/utils/alert-management";

// Enhanced seed script with comprehensive payroll scenarios
async function main() {
  const prisma = new PrismaClient();

  // Delete all data before seeding
  console.log("🗑️  Deleting all existing data...");
  try {
    // Delete in correct order to respect foreign key constraints
    await prisma.alert.deleteMany();
    await prisma.payslipItem.deleteMany();
    await prisma.payslip.deleteMany();
    await prisma.form101.deleteMany();
    await prisma.leaveRecord.deleteMany();
    await prisma.bankAccount.deleteMany();
    await prisma.salaryRecord.deleteMany();
    await prisma.employee.deleteMany();
    await prisma.department.deleteMany();
    await prisma.user.deleteMany();
    await prisma.employer.deleteMany();
    await prisma.tenant.deleteMany();

    console.log("✅ All data deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting data:", error);
    throw error;
  }

  const password = "*********";
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  console.log("🚀 Starting enhanced seed with payroll improvements...");

  try {
    // Create tenants
    const defaultTenant = await prisma.tenant.upsert({
      where: { name: "default" },
      update: {},
      create: { name: "default", plan: "PREMIUM" }
    });

    const talsTenant = await prisma.tenant.upsert({
      where: { name: "TALS" },
      update: {},
      create: { name: "TALS", plan: "PREMIUM" }
    });

    console.log("✅ Tenants created/updated");

    // Create employers
    const smartchiEmployer = await prisma.employer.upsert({
      where: {
        tenantId_name: {
          tenantId: defaultTenant.id,
          name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ"
        }
      },
      update: {},
      create: {
        name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ",
        identifier: "*********",
        companyId: "*********",
        tenantId: defaultTenant.id,
        taxId: "*********",
        niNumber: "*********",
        industry: Sector.CONSTRUCTION,
        payrollDay: 10,
        address: {
          street: "הברזל 30",
          city: "באר שבע",
          country: "ישראל",
          zipCode: "7670000"
        },
        contact: {
          name: "אברהם טורגמן",
          email: "<EMAIL>",
          phone: "050-2466626",
          position: "מנכ\"ל"
        }
      }
    });

    console.log("✅ Employers created");

    // Create departments
    const constructionDept = await prisma.department.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "עובדי בניין זרים"
        }
      },
      update: {},
      create: {
        name: "עובדי בניין זרים",
        code: "CONST_FOREIGN",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת עובדי בניין זרים"
      }
    });

    const adminDept = await prisma.department.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "הנהלה"
        }
      },
      update: {},
      create: {
        name: "הנהלה",
        code: "ADMIN",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת הנהלה"
      }
    });

    console.log("✅ Departments created");

    // Create users
    await prisma.user.upsert({
      where: {
        tenantId_email: {
          tenantId: defaultTenant.id,
          email: "<EMAIL>"
        }
      },
      update: { password: hashedPassword },
      create: {
        email: "<EMAIL>",
        name: "אברהם טורגמן",
        password: hashedPassword,
        role: Role.OWNER,
        tenantId: defaultTenant.id,
        isActive: true
      }
    });

    await prisma.user.upsert({
      where: {
        tenantId_email: {
          tenantId: defaultTenant.id,
          email: "<EMAIL>"
        }
      },
      update: { password: hashedPassword },
      create: {
        email: "<EMAIL>",
        name: "מנהל שכר",
        password: hashedPassword,
        role: Role.ACCOUNTANT,
        tenantId: defaultTenant.id,
        employerId: smartchiEmployer.id,
        isActive: true
      }
    });

    console.log("✅ Users created");

    // Create comprehensive employee test cases
    const testEmployees = [
      // Case 1: Foreign worker with visa expiring soon (Critical alert)
      {
        firstName: "Somchai",
        lastName: "Jaidee",
        nationalId: "FW001",
        startDate: new Date("2023-01-15"),
        birthDate: new Date("1985-03-20"),
        contact: { email: "<EMAIL>", phone: "050-1111111" },
        address: { street: "מגורי עובדים א", city: "אשדוד", country: "ישראל", zipCode: "7700000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Thailand",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VTHAI001",
        visaExpiry: new Date(new Date().getTime() + 25 * 24 * 60 * 60 * 1000), // 25 days from now
        visaType: "B1-Construction",
        baseSalary: 6200, // Just above minimum wage
        workedHours: 220, // Excessive hours
        overtimeHours: { h125: 40, h150: 30, h175: 10, h200: 5 },
      },

      // Case 2: Foreign worker with expired visa (Critical compliance issue)
      {
        firstName: "Wei",
        lastName: "Zhang",
        nationalId: "FW002",
        startDate: new Date("2022-06-01"),
        birthDate: new Date("1990-07-15"),
        contact: { email: "<EMAIL>", phone: "050-2222222" },
        address: { street: "מגורי עובדים ב", city: "נתניה", country: "ישראל", zipCode: "4200000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "China",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.COLLECTIVE,
        visaNumber: "VCHN001",
        visaExpiry: new Date("2024-12-01"), // Already expired
        visaType: "B1-Construction",
        baseSalary: 5500, // Below minimum wage
        workedHours: 186,
        overtimeHours: { h125: 20, h150: 10, h175: 0, h200: 0 },
      },

      // Case 3: Foreign worker missing Form 101 (90+ days)
      {
        firstName: "Giorgi",
        lastName: "Kapanadze",
        nationalId: "FW003",
        startDate: new Date("2024-01-01"), // Over 90 days ago
        birthDate: new Date("1988-11-30"),
        contact: { email: "<EMAIL>", phone: "050-3333333" },
        address: { street: "מגורי עובדים ג", city: "חיפה", country: "ישראל", zipCode: "3300000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Georgia",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VGEO001",
        visaExpiry: new Date("2025-12-31"),
        visaType: "B1-Construction",
        baseSalary: 7800,
        workedHours: 200,
        overtimeHours: { h125: 25, h150: 15, h175: 5, h200: 0 },
        hasForm101: false, // Missing Form 101
      },

      // Case 4: Foreign worker with deposit compliance issue
      {
        firstName: "Vasile",
        lastName: "Popescu",
        nationalId: "FW004",
        startDate: new Date("2023-03-15"),
        birthDate: new Date("1992-05-10"),
        contact: { email: "<EMAIL>", phone: "050-4444444" },
        address: { street: "מגורי עובדים ד", city: "באר שבע", country: "ישראל", zipCode: "8400000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Moldova",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VMOL001",
        visaExpiry: new Date("2025-08-15"),
        visaType: "B1-Construction",
        baseSalary: 8500,
        workedHours: 190,
        overtimeHours: { h125: 30, h150: 10, h175: 0, h200: 0 },
        depositIssue: true, // Will create insufficient deposit
      },

      // Case 5: Israeli employee with excessive deductions
      {
        firstName: "משה",
        lastName: "כהן",
        nationalId: "*********",
        startDate: new Date("2022-01-01"),
        birthDate: new Date("1980-01-15"),
        contact: { email: "<EMAIL>", phone: "050-5555555" },
        address: { street: "הרצל 45", city: "תל אביב", country: "ישראל", zipCode: "6100000" },
        departmentId: adminDept.id,
        isForeign: false,
        baseSalary: 15000,
        workedHours: 186,
        overtimeHours: { h125: 10, h150: 5, h175: 0, h200: 0 },
        excessiveDeductions: true, // Will create unusual deductions
      },

      // Case 6: Foreign worker with all compliance issues
      {
        firstName: "Aziz",
        lastName: "Rahimov",
        nationalId: "FW005",
        startDate: new Date("2023-11-01"),
        birthDate: new Date("1995-09-20"),
        contact: { email: "<EMAIL>", phone: "050-6666666" },
        address: { street: "מגורי עובדים ה", city: "אילת", country: "ישראל", zipCode: "8800000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Uzbekistan",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VUZB001",
        visaExpiry: new Date(new Date().getTime() + 45 * 24 * 60 * 60 * 1000), // 45 days
        visaType: "B1-Construction",
        baseSalary: 5700, // Below minimum
        workedHours: 250, // Excessive
        overtimeHours: { h125: 50, h150: 40, h175: 20, h200: 10 }, // Way over limits
        hasForm101: false,
        depositIssue: true,
      },
    ];

    // Create employees and their payroll data
    for (const empData of testEmployees) {
      const employee = await prisma.employee.create({
        data: {
          tenantId: defaultTenant.id,
          employerId: smartchiEmployer.id,
          firstName: empData.firstName,
          lastName: empData.lastName,
          nationalId: empData.nationalId,
          startDate: empData.startDate,
          birthDate: empData.birthDate,
          contact: empData.contact,
          status: "ACTIVE",
          address: empData.address,
          departmentId: empData.departmentId,
          isForeign: empData.isForeign,
          country: empData.country,
          sector: empData.sector,
          agreementType: empData.agreementType,
          isResidentForNI: !empData.isForeign,
          visaNumber: empData.visaNumber,
          visaExpiry: empData.visaExpiry,
          visaType: empData.visaType,
          baseSalary: empData.baseSalary,
        }
      });

      console.log(`✅ Created employee: ${employee.firstName} ${employee.lastName}`);

      // Create salary record
      await prisma.salaryRecord.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          basis: "MONTHLY",
          currency: "ILS",
          payFrequency: "MONTHLY",
          amount: empData.baseSalary,
          effectiveFrom: empData.startDate,
          positionType: "MONTHLY",
          positionPercentage: 100,
          standardMonthlyHours: 186,
          workedHours: empData.workedHours,
          hourlyRate: empData.baseSalary / 186,
        }
      });

      // Create bank account
      await prisma.bankAccount.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          bankName: "בנק הפועלים",
          bankCode: 12,
          branchCode: "456",
          branchNumber: 456,
          accountNumber: Math.floor(100000 + Math.random() * 900000).toString(),
          currency: "ILS",
          accountType: "CHECKING",
          isPrimary: true,
        }
      });

      // Create Form 101 if applicable
      if (empData.hasForm101 !== false) {
        await prisma.form101.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            taxYear: 2024,
            maritalStatus: MaritalStatus.SINGLE,
            spouseWorks: false,
            childrenCount: 0,
            isMainEmployer: true,
            hasAdditionalIncome: false,
            signedAt: new Date(empData.startDate.getTime() + 7 * 24 * 60 * 60 * 1000),
          }
        });
      }

      // Create multiple payslips to show different scenarios
      const months = [
        { year: 2024, month: 10, status: PayslipStatus.PAID },
        { year: 2024, month: 11, status: PayslipStatus.APPROVED },
        { year: 2024, month: 12, status: PayslipStatus.CALCULATED },
        { year: 2025, month: 1, status: PayslipStatus.DRAFT },
      ];

      for (const period of months) {
        const payslipDate = new Date(period.year, period.month - 1, 1);
        const periodEnd = new Date(period.year, period.month, 0);

        // Calculate overtime pay
        const hourlyRate = empData.baseSalary / 186;
        const overtimeCalc = calculateOvertimePay(
          empData.workedHours - (empData.overtimeHours?.h125 || 0) - (empData.overtimeHours?.h150 || 0) -
          (empData.overtimeHours?.h175 || 0) - (empData.overtimeHours?.h200 || 0),
          empData.overtimeHours?.h125 || 0,
          empData.overtimeHours?.h150 || 0,
          empData.overtimeHours?.h175 || 0,
          empData.overtimeHours?.h200 || 0,
          hourlyRate
        );

        const grossPay = overtimeCalc.totalPay;

        // Calculate tax
        const taxCalc = calculateIncomeTax(
          grossPay,
          empData.isForeign ? 0 : 2.25, // Tax credits
          true,
          0
        );

        // Calculate National Insurance
        const niCalc = calculateNationalInsurance(
          grossPay,
          !empData.isForeign,
          new Date().getFullYear() - (empData.birthDate?.getFullYear() || 1990)
        );

        // Calculate foreign worker deductions
        let foreignWorkerDeductions = {
          housingDeduction: 0,
          transportDeduction: 0,
          healthInsuranceDeduction: 0,
          totalDeductions: 0
        };

        if (empData.isForeign && empData.sector) {
          foreignWorkerDeductions = calculateForeignWorkerDeductions(
            empData.country || "",
            empData.sector,
            grossPay,
            empData.address.zipCode
          );
        }

                 // Create payslip items
         const payslipItems: Array<{
           description: string;
           amount: number;
           type: "EARNING" | "DEDUCTION" | "EMPLOYER_CONTRIB" | "REIMBURSEMENT";
           kod?: PayslipItemKod;
           units?: number;
           rate?: number;
           percentage?: number;
           isDebit: boolean;
         }> = [
           {
             description: "שכר בסיס",
             amount: empData.baseSalary,
             type: "EARNING",
             kod: PayslipItemKod.K_0100,
             isDebit: false,
           },
         ];

        // Add overtime items
        if (overtimeCalc.overtime125Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 125%",
            amount: overtimeCalc.overtime125Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1001,
            units: empData.overtimeHours?.h125 || 0,
            rate: hourlyRate * 1.25,
            percentage: 125,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime150Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 150%",
            amount: overtimeCalc.overtime150Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1002,
            units: empData.overtimeHours?.h150 || 0,
            rate: hourlyRate * 1.50,
            percentage: 150,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime175Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 175%",
            amount: overtimeCalc.overtime175Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1003,
            units: empData.overtimeHours?.h175 || 0,
            rate: hourlyRate * 1.75,
            percentage: 175,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime200Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 200%",
            amount: overtimeCalc.overtime200Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1004,
            units: empData.overtimeHours?.h200 || 0,
            rate: hourlyRate * 2.00,
            percentage: 200,
            isDebit: false,
          });
        }

        // Add deductions
        payslipItems.push({
          description: "מס הכנסה",
          amount: taxCalc.finalTax,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_TAX,
          isDebit: true,
        });

        payslipItems.push({
          description: "ביטוח לאומי עובד",
          amount: niCalc.employeeNI,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_NI_EMP,
          isDebit: true,
        });

        payslipItems.push({
          description: "ביטוח בריאות",
          amount: niCalc.healthInsurance,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_NI_HEALTH,
          isDebit: true,
        });

        // Foreign worker specific deductions
        if (empData.isForeign) {
          payslipItems.push({
            description: "ניכוי מגורים",
            amount: foreignWorkerDeductions.housingDeduction,
            type: "DEDUCTION",
            kod: PayslipItemKod.K_1020,
            isDebit: true,
          });

          payslipItems.push({
            description: "ניכוי ביטוח רפואי",
            amount: foreignWorkerDeductions.healthInsuranceDeduction,
            type: "DEDUCTION",
            kod: PayslipItemKod.K_1021,
            isDebit: true,
          });
        }

        // Add excessive deductions for test case
        if (empData.excessiveDeductions) {
          payslipItems.push({
            description: "קנס איחורים",
            amount: grossPay * 0.25, // 25% - unusual
            type: "DEDUCTION",
            isDebit: true,
          });

          payslipItems.push({
            description: "החזר הלוואה",
            amount: grossPay * 0.20, // 20% - high
            type: "DEDUCTION",
            isDebit: true,
          });
        }

        // Calculate totals
        const totalDeductions = payslipItems
          .filter(item => item.isDebit)
          .reduce((sum, item) => sum + item.amount, 0);

        let netPay = grossPay - totalDeductions;

        // Handle deposit for foreign workers
        let depositAmount = 0;
        if (empData.isForeign) {
          depositAmount = empData.depositIssue ? grossPay * 0.05 : grossPay * 0.20; // 5% if issue, 20% normal
          netPay -= depositAmount;
        }

        // Create payslip
        const payslip = await prisma.payslip.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            year: period.year,
            month: period.month,
            periodStart: payslipDate,
            periodEnd: periodEnd,
            status: period.status,
            grossPay: grossPay,
            netPay: Math.max(0, netPay), // Ensure non-negative
            taxDeducted: taxCalc.finalTax,
            insuranceDeducted: niCalc.employeeNI + niCalc.healthInsurance,
            otherDeductions: totalDeductions - taxCalc.finalTax - niCalc.employeeNI - niCalc.healthInsurance,
            healthInsurance: empData.isForeign ? foreignWorkerDeductions.healthInsuranceDeduction : undefined,
            pensionEmployee: empData.isForeign ? grossPay * 0.06 : undefined,
            pensionEmployer: empData.isForeign ? grossPay * 0.065 : undefined,
            severancePay: empData.isForeign ? grossPay * 0.0833 : undefined,
            netDeposit: depositAmount > 0 ? depositAmount : undefined,
            currency: "ILS",
            items: {
              create: payslipItems
            }
          }
        });

        console.log(`  📄 Created payslip for ${period.month}/${period.year} - Status: ${period.status}`);
      }
    }

    // Generate alerts based on the data
    console.log("\n🚨 Generating compliance alerts...");

    // Get all employees with their related data
    const allEmployees = await prisma.employee.findMany({
      where: { tenantId: defaultTenant.id },
      include: {
        form101: true,
        payslips: {
          where: { year: 2025, month: 1 },
          include: { items: true }
        }
      }
    });

    // Generate visa expiry alerts
    const visaAlerts = generateVisaExpiryAlerts(
      allEmployees.map(emp => ({
        id: emp.id,
        firstName: emp.firstName,
        lastName: emp.lastName,
        isForeign: emp.isForeign,
        visaExpiry: emp.visaExpiry || undefined,
        country: emp.country || undefined,
      }))
    );

         // Generate Form 101 alerts
     const form101Alerts = generateMissingForm101Alerts(
       allEmployees.map(emp => ({
         id: emp.id,
         firstName: emp.firstName,
         lastName: emp.lastName,
         startDate: emp.startDate,
         form101: emp.form101s?.[0] ? {
           id: emp.form101s[0].id,
           signedAt: emp.form101s[0].signedAt || undefined
         } : null,
       }))
     );

    // Generate overtime alerts
    const overtimeAlerts = generateOvertimeLimitAlerts(
      allEmployees.flatMap(emp =>
        emp.payslips.map(payslip => ({
          employeeId: emp.id,
          employeeName: `${emp.firstName} ${emp.lastName}`,
          year: payslip.year,
          month: payslip.month,
          items: payslip.items.map(item => ({
            kod: item.kod || undefined,
            units: item.units ? Number(item.units) : undefined,
            percentage: item.percentage ? Number(item.percentage) : undefined,
          }))
        }))
      )
    );

    // Generate deposit compliance alerts
    const depositAlerts = generateDepositComplianceAlerts(
      allEmployees.map(emp => ({
        id: emp.id,
        firstName: emp.firstName,
        lastName: emp.lastName,
        isForeign: emp.isForeign,
        grossSalary: Number(emp.baseSalary || 0),
        netDeposit: emp.payslips[0]?.netDeposit ? Number(emp.payslips[0].netDeposit) : undefined,
        country: emp.country || undefined,
      }))
    );

    // Create alerts in database
    const allAlerts = [...visaAlerts, ...form101Alerts, ...overtimeAlerts, ...depositAlerts];

    for (const alert of allAlerts) {
             await prisma.alert.create({
         data: {
           tenantId: defaultTenant.id,
           employeeId: alert.employeeId,
           type: alert.type,
           category: alert.category,
           message: alert.message,
           context: alert.context as any, // Prisma JSON field
           dueDate: alert.dueDate,
           severity: alert.severity,
           isRead: false,
           isResolved: false,
         }
       });
    }

    console.log(`✅ Created ${allAlerts.length} compliance alerts`);

    // Summary report
    console.log("\n📊 Seed Summary:");
    console.log("================");
    console.log(`Tenants: 2`);
    console.log(`Employers: 1`);
    console.log(`Departments: 2`);
    console.log(`Employees: ${testEmployees.length}`);
    console.log(`Payslips: ${testEmployees.length * 4}`);
    console.log(`Alerts: ${allAlerts.length}`);
    console.log("\n🎯 Test Scenarios Created:");
    console.log("- Visa expiring soon (Critical)");
    console.log("- Expired visa");
    console.log("- Missing Form 101 (90+ days)");
    console.log("- Deposit compliance issues");
    console.log("- Excessive overtime hours");
    console.log("- Below minimum wage");
    console.log("- Unusual deductions");
    console.log("- All payslip statuses (DRAFT, CALCULATED, APPROVED, PAID)");

    console.log("\n✅ Enhanced seed completed successfully!");
    console.log(`\n🔑 Login credentials: password = ${password}`);

  } catch (error) {
    console.error("❌ Error in enhanced seed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log("\n🎉 Enhanced seed completed!"))
  .catch((e) => {
    console.error("💥 Enhanced seed failed:", e);
    process.exit(1);
  });