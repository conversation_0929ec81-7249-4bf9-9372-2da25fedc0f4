"use client";

import { useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Card, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Clock, FileText } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface SignaturePageData {
  form101: {
    id: string;
    taxYear: number;
    maritalStatus: string;
    spouseWorks: boolean;
    childrenCount: number;
    isMainEmployer: boolean;
    hasAdditionalIncome: boolean;
  };
  employee: {
    firstName: string;
    lastName: string;
    nationalId: string;
  };
  expiresAt: Date;
  status: 'pending' | 'signed' | 'expired' | 'cancelled';
}

export default function SignaturePage() {
  const params = useParams();
  const signatureId = params?.id as string;
  
  const [data, setData] = useState<SignaturePageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [signing, setSigning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (signatureId) {
      fetchSignatureData();
    }
  }, [signatureId]);

  const fetchSignatureData = async () => {
    try {
      // TODO: Implement API call to get signature data
      // For now, mock data
      setTimeout(() => {
        setData({
          form101: {
            id: "mock-id",
            taxYear: 2024,
            maritalStatus: "SINGLE",
            spouseWorks: false,
            childrenCount: 0,
            isMainEmployer: true,
            hasAdditionalIncome: false,
          },
          employee: {
            firstName: "יוסי",
            lastName: "כהן",
            nationalId: "*********",
          },
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          status: 'pending'
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      setError("שגיאה בטעינת נתוני החתימה");
      setLoading(false);
    }
  };

  const handleSign = async () => {
    setSigning(true);
    try {
      // TODO: Implement actual signature logic
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setData(prev => prev ? { ...prev, status: 'signed' } : null);
      setSigning(false);
    } catch (error) {
      setError("שגיאה בחתימה על הטופס");
      setSigning(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Skeleton className="h-8 w-48 mb-4" />
            <Skeleton className="h-4 w-32 mb-6" />
            <Skeleton className="h-32 w-full mb-4" />
            <Skeleton className="h-10 w-32" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-xl font-semibold mb-2">שגיאה</h1>
            <p className="text-gray-600 mb-4">{error || "לא ניתן לטעון את נתוני החתימה"}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isExpired = new Date() > data.expiresAt;
  const isSigned = data.status === 'signed';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <FileText className="h-6 w-6" />
            <CardTitle>חתימה על טופס 101</CardTitle>
          </div>
          <p className="text-sm text-gray-600">
            שנת מס {data.form101.taxYear}
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Employee Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי העובד</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">שם:</span> {data.employee.firstName} {data.employee.lastName}</p>
              <p><span className="font-medium">תעודת זהות:</span> {data.employee.nationalId}</p>
            </div>
          </div>

          {/* Form Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי הטופס</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">מצב משפחתי:</span> {data.form101.maritalStatus === 'SINGLE' ? 'רווק/ה' : data.form101.maritalStatus}</p>
              <p><span className="font-medium">מספר ילדים:</span> {data.form101.childrenCount}</p>
              <p><span className="font-medium">מעסיק עיקרי:</span> {data.form101.isMainEmployer ? 'כן' : 'לא'}</p>
              <p><span className="font-medium">הכנסה נוספת:</span> {data.form101.hasAdditionalIncome ? 'כן' : 'לא'}</p>
            </div>
          </div>

          {/* Status */}
          <div className="text-center">
            {isSigned ? (
              <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">הטופס נחתם בהצלחה</span>
              </div>
            ) : isExpired ? (
              <div className="flex items-center justify-center gap-2 text-red-600 mb-4">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">פג תוקף הקישור לחתימה</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2 text-blue-600 mb-4">
                <Clock className="h-5 w-5" />
                <span className="font-medium">ממתין לחתימה</span>
              </div>
            )}

            <p className="text-sm text-gray-600 mb-6">
              תוקף הקישור: {data.expiresAt.toLocaleDateString('he-IL')}
            </p>

            {!isSigned && !isExpired && (
              <Button 
                onClick={handleSign} 
                disabled={signing}
                className="w-full max-w-xs"
              >
                {signing ? "חותם..." : "חתום על הטופס"}
              </Button>
            )}
          </div>

          {/* Legal Notice */}
          <div className="text-xs text-gray-500 text-center border-t pt-4">
            <p>בחתימה על טופס זה אני מאשר/ת כי הפרטים נכונים ומדויקים.</p>
            <p>החתימה מהווה הסכמה לעיבוד הנתונים לצורכי חישוב מס הכנסה.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
